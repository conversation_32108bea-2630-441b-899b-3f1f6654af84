.markdown {
  color: var(--color-text);
  line-height: 1.6;
  user-select: text;
  word-break: break-word;

  h1:first-child,
  h2:first-child,
  h3:first-child,
  h4:first-child,
  h5:first-child,
  h6:first-child {
    margin-top: 0;
  }

  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    margin: 1.5em 0 1em 0;
    line-height: 1.3;
    font-weight: bold;
    font-family: var(--font-family);
  }

  h1 {
    margin-top: 0;
    font-size: 2em;
    border-bottom: 0.5px solid var(--color-border);
    padding-bottom: 0.3em;
  }

  h2 {
    font-size: 1.5em;
    border-bottom: 0.5px solid var(--color-border);
    padding-bottom: 0.3em;
  }

  h3 {
    font-size: 1.2em;
  }

  h4 {
    font-size: 1em;
  }

  h5 {
    font-size: 0.9em;
  }

  h6 {
    font-size: 0.8em;
  }

  p {
    margin: 1.3em 0;
    white-space: pre-wrap;
    line-height: 1.6;

    &:last-child {
      margin-bottom: 5px;
    }

    &:first-child {
      margin-top: 0;
    }

    &:has(+ ul) {
      margin-bottom: 0;
    }
  }

  ul {
    list-style: initial;
  }

  ul,
  ol {
    padding-left: 1.5em;
    margin: 1em 0;
  }

  li {
    margin-bottom: 0.5em;
    pre {
      margin: 1.5em 0 !important;
    }
    &::marker {
      color: var(--color-text-3);
    }
  }

  li > ul,
  li > ol {
    margin: 0.5em 0;
  }

  hr {
    border: none;
    border-top: 0.5px solid var(--color-border);
    margin: 20px 0;
  }

  span {
    white-space: pre;
  }

  p code,
  li code {
    background: var(--color-background-mute);
    padding: 3px 5px;
    margin: 0 2px;
    border-radius: 5px;
    word-break: keep-all;
    white-space: pre;
  }

  code {
    font-family: var(--code-font-family);
  }

  pre {
    border-radius: 8px;
    overflow-x: auto;
    font-family: 'Fira Code', 'Courier New', Courier, monospace;
    background-color: var(--color-background-mute);
    &:has(.special-preview) {
      background-color: transparent;
    }
    &:not(pre pre) {
      > code:not(pre pre > code) {
        padding: 15px;
        display: block;
      }
    }
    pre {
      margin: 0 !important;
      code {
        background: none;
        padding: 0;
        border-radius: 0;
      }
    }
  }

  pre + pre {
    margin-top: 10px;
  }

  blockquote {
    margin: 1.5em 0;
    padding: 1em 1.5em;
    background-color: var(--color-background-soft);
    border-left: 4px solid var(--color-primary);
    border-radius: 0 8px 8px 0;
    font-style: italic;
    position: relative;
  }

  table {
    --table-border-radius: 8px;
    margin: 2em 0;
    font-size: 0.9em;
    width: 100%;
    border-radius: var(--table-border-radius);
    overflow: hidden;
    border-collapse: separate;
    border: 0.5px solid var(--color-border);
    border-spacing: 0;
  }

  th,
  td {
    border-right: 0.5px solid var(--color-border);
    border-bottom: 0.5px solid var(--color-border);
    padding: 0.5em;
    &:last-child {
      border-right: none;
    }
  }

  tr:last-child td {
    border-bottom: none;
  }

  th {
    background-color: var(--color-background-mute);
    font-weight: 600;
    font-family: var(--font-family);
    text-align: left;
  }

  tr:hover {
    background-color: var(--color-background-soft);
  }

  img {
    max-width: 100%;
    height: auto;
    margin: 10px 0;
  }

  a,
  .link {
    color: var(--color-link);
    text-decoration: none;
    cursor: pointer;

    &:hover {
      text-decoration: underline;
    }
  }

  strong {
    font-weight: bold;
  }

  em {
    font-style: italic;
  }

  del {
    text-decoration: line-through;
  }

  sup,
  sub {
    font-size: 75%;
    line-height: 0;
    position: relative;
    vertical-align: baseline;
  }

  sup {
    top: -0.5em;
    border-radius: 50%;
    background-color: var(--color-reference);
    color: var(--color-reference-text);
    padding: 2px 5px;
    zoom: 0.8;
    & > span.link {
      color: var(--color-reference-text);
    }
  }

  sub {
    bottom: -0.25em;
  }

  .footnote-ref {
    font-size: 0.8em;
    vertical-align: super;
    line-height: 0;
    margin: 0 2px;
    color: var(--color-primary);
    text-decoration: none;

    &:hover {
      text-decoration: underline;
    }
  }

  > *:last-child {
    margin-bottom: 0 !important;
  }
}

.footnotes {
  margin-top: 1em;
  margin-bottom: 1em;
  padding-top: 1em;

  background-color: var(--color-reference-background);
  border-radius: 8px;
  padding: 8px 12px;

  h4 {
    margin-bottom: 5px;
    font-size: 12px;
  }

  a {
    color: var(--color-link);
  }

  ol {
    padding-left: 1em;
    margin: 0;
    li:last-child {
      margin-bottom: 0;
    }
  }

  li {
    font-size: 0.9em;
    margin-bottom: 0.5em;
    color: var(--color-text-light);

    p {
      display: inline;
      margin: 0;
    }
  }

  .footnote-backref {
    font-size: 0.8em;
    vertical-align: super;
    line-height: 0;
    margin-left: 5px;
    color: var(--color-primary);
    text-decoration: none;

    &:hover {
      text-decoration: underline;
    }
  }
}

emoji-picker {
  --border-size: 0;
}

.katex,
mjx-container {
  display: inline-block;
  overflow-x: auto;
  overflow-y: hidden;
  overflow-wrap: break-word;
  vertical-align: middle;
  max-width: 100%;
  padding: 1px 2px;
  margin-top: -2px;
}

/* Shiki 相关样式 */
.shiki {
  font-family: var(--code-font-family);
  // 保持行高为初始值，在 shiki 代码块中处理
  line-height: initial;
}

/* CodeMirror 相关样式 */
.cm-editor {
  border-radius: inherit;

  &.cm-focused {
    outline: none;
  }

  .cm-scroller {
    font-family: var(--code-font-family);
    border-radius: inherit;

    .cm-gutters {
      line-height: 1.6;
      border-right: none;
    }

    .cm-content {
      line-height: 1.6;
      padding-left: 0.25em;
    }

    .cm-lineWrapping * {
      word-wrap: break-word;
      white-space: pre-wrap;
    }
  }
}
