{"translation": {"agents": {"add.button": "Προσθήκη στο Βοηθό", "add.knowledge_base": "Βάση γνώσεων", "add.knowledge_base.placeholder": "Επιλέξτε βάση γνώσεων", "add.name": "Όνομα", "add.name.placeholder": "Εισαγάγετε όνομα", "add.prompt": "Φράση προκαλέσεως", "add.prompt.placeholder": "Εισαγάγετε φράση προκαλέσεως", "add.prompt.variables.tip": {"content": "{{date}}:\tΗμερομηνία\n{{time}}:\tΏρα\n{{datetime}}:\tΗμερομηνία και ώρα\n{{system}}:\tΛειτουργικό σύστημα\n{{arch}}:\tΑρχιτεκτονική CPU\n{{language}}:\tΓλώσσ<PERSON>\n{{model_name}}:\tΌνομα μοντέλου\n{{username}}:\tΌνομα χρήστη", "title": "Διαθέσιμες μεταβλητές"}, "add.title": "Δημιουργ<PERSON>α νέου ειδικού", "delete.popup.content": "Είστε σίγουροι ότι θέλετε να διαγράψετε αυτόν τον ειδικό;", "edit.model.select.title": "Επιλογή μοντέλου", "edit.title": "Επεξεργασία ειδικού", "export": {"agent": "Εξαγωγή υποκειμένου"}, "import": {"button": "Εισαγωγή", "error": {"fetch_failed": "Αποτυχία λήψης δεδομένων από το URL", "invalid_format": "Μη έγκυρη μορφή πράκτορα: λείπουν υποχρεωτικά πεδία", "url_required": "Παρα<PERSON><PERSON><PERSON><PERSON> εισάγετε τη διεύθυνση URL"}, "file_filter": "Αρχεία JSON", "select_file": "Επιλέξτε αρχείο", "title": "Εισαγωγή από το εξωτερικό", "type": {"file": "Αρχείο", "url": "URL"}, "url_placeholder": "Εισάγετε τη διεύθυνση URL JSON"}, "manage.title": "Διαχείριση ειδικών", "my_agents": "Οι ειδικοί μου", "search.no_results": "Δεν βρέθηκαν σχετικοί ειδικοί", "sorting.title": "Ταξινόμηση", "tag.agent": "Ειδικ<PERSON>ς", "tag.default": "Προεπιλογή", "tag.new": "<PERSON><PERSON><PERSON>", "tag.system": "Σύστημα", "title": "Ειδικοί"}, "assistants": {"abbr": "<PERSON><PERSON><PERSON><PERSON><PERSON>ς", "clear.content": "Η διαγραφή του θέματος θα διαγράψει όλα τα θέματα και τα αρχεία του βοηθού. Είστε σίγουροι πως θέλετε να συνεχίσετε;", "clear.title": "Διαγρα<PERSON><PERSON> θέματος", "copy.title": "Αντιγραφή βοηθού", "delete.content": "Η διαγραφή του βοηθού θα διαγράψει όλα τα θέματα και τα αρχεία που είναι συνδεδεμένα με αυτόν. Είστε σίγουροι πως θέλετε να συνεχίσετε;", "delete.title": "Διαγρα<PERSON><PERSON> βοηθού", "edit.title": "Επεξεργασία βοηθού", "icon.type": "Εικόνα Βοηθού", "save.success": "Η αποθήκευση ολοκληρώθηκε επιτυχώς", "save.title": "Αποθήκευση στον νοητή", "search": "Αναζήτηση βοηθού", "settings.default_model": "Προεπιλεγ<PERSON><PERSON>νο μοντέλο", "settings.knowledge_base": "Ρυθμίσεις βάσης γνώσεων", "settings.knowledge_base.recognition": "Κλήση βάσης γνώσης", "settings.knowledge_base.recognition.off": "Υποχρεωτική αναζήτηση", "settings.knowledge_base.recognition.on": "Αναγνώριση πρόθεσης", "settings.knowledge_base.recognition.tip": "Ο πράκτορας θα καλέσει τη δυνατότητα αναγνώρισης πρόθεσης του μεγάλου μοντέλου για να αποφασίσει αν χρειάζεται να κληθεί η βάση γνώσης για να απαντηθεί, και αυτή η λειτουργία θα εξαρτηθεί από τις δυνατότητες του μοντέλου", "settings.mcp": "Διακομιστής MCP", "settings.mcp.description": "Διακο<PERSON>ιστής MCP που είναι ενεργοποιημένος εξ ορισμού", "settings.mcp.enableFirst": "Πρώτα ενεργοποιήστε αυτόν τον διακομιστή στις ρυθμίσεις MCP", "settings.mcp.noServersAvailable": "Δεν υπάρχουν διαθέσιμοι διακομιστές MCP. Προσθέστε ένα διακομιστή στις ρυθμίσεις", "settings.mcp.title": "Ρυθμίσεις MCP", "settings.model": "Ρυθμίσεις μοντέλου", "settings.more": "Ρυθμίσεις Βοηθού", "settings.prompt": "Ρυθμίσεις προκαλύμματος", "settings.reasoning_effort": "<PERSON><PERSON><PERSON><PERSON> λογισμικού αλυσίδας", "settings.reasoning_effort.default": "Προεπιλογή", "settings.reasoning_effort.high": "Μεγάλο", "settings.reasoning_effort.low": "Μικρό", "settings.reasoning_effort.medium": "Μεσαίο", "settings.reasoning_effort.off": "Απενεργοποίηση", "settings.regular_phrases": {"add": "Προσθήκη φράσης", "contentLabel": "Περιεχόμενο", "contentPlaceholder": "Παρα<PERSON><PERSON><PERSON><PERSON> εισάγετε το περιεχόμενο της φράσης. Υποστηρίζονται μεταβλητές, και στη συνέχεια πατήστε Tab για να μεταβείτε γρήγορα στη μεταβλητή και να την επεξεργαστείτε. Για παράδειγμα: \\nΒοήθησέ με να σχεδιάσω μια διαδρομή από το ${from} στο ${to}, και στη συνέχεια στείλε την στο ${email}.", "delete": "Διαγραφή φράσης", "deleteConfirm": "Είστε βέβ<PERSON><PERSON>ος ότι θέλετε να διαγράψετε αυτήν τη φράση;", "edit": "Επεξεργασία φράσης", "title": "Δημοφιλείς φράσεις", "titleLabel": "Τίτλος", "titlePlaceholder": "Εισαγάγετε τίτλο"}, "settings.title": "Ρυθμίσεις Βοηθού", "title": "<PERSON><PERSON><PERSON><PERSON><PERSON>ς"}, "auth": {"error": "Αποτυχ<PERSON><PERSON> στην αυτόματη πήγαινη των κλειδιών, παρακαλείστε να το κάνετε χειροκίνητα", "get_key": "Πήγαινη", "get_key_success": "Η αυτόματη πήγαινη των κλειδιών ήταν επιτυχής", "login": "Είσοδος", "oauth_button": "Είσοδος με {{provider}}"}, "backup": {"confirm": "Είστε σίγουροι ότι θέλετε να αντιγράψετε τα δεδομένα;", "confirm.button": "Επιλογή μοντέλου αντιγράφου προσωρινής αποθήκευσης", "content": "Αντιγράφετε όλα τα δεδομένα, συμπεριλαμβανομένων των εγγραφών συζήτησης, των ρυθμίσεων, της βάσης γνώσεων και όλων των δεδομένων. Παρακαλούμε σημειώστε ότι η διαδικασία αντιγράφου μπορεί να χρειαστεί λίγο χρόνο. Ευχαριστούμε για την υπομονή.", "progress": {"completed": "Η αντιγραφή ασφαλείας ολοκληρώθηκε", "compressing": "Συμπίεση αρχείων...", "copying_files": "Αντιγραφή αρχείων... {{progress}}%", "preparing": "Ετοιμασία αντιγράφου ασφαλείας...", "title": "<PERSON>ρ<PERSON><PERSON><PERSON><PERSON> αντιγράφου ασφαλείας", "writing_data": "Εγγραφή δεδομένων..."}, "title": "Αντιγρα<PERSON><PERSON> Δεδομένων"}, "button": {"add": "προσθέστε", "added": "προστέθηκε", "collapse": "συμπεριλάβετε", "manage": "χειριστείτε", "select_model": "επιλογή μοντέλου", "show.all": "δείξτε όλα", "update_available": "Υπάρχει διαθέσιμη ενημέρωση"}, "chat": {"add.assistant.title": "Προσθήκη βοηθού", "artifacts.button.download": "Λή<PERSON>η", "artifacts.button.openExternal": "Άνοιγμα στο εξωτερικό περιηγητή", "artifacts.button.preview": "Προεπισκόπηση", "artifacts.preview.openExternal.error.content": "Σφάλμα κατά την άνοιγμα στο εξωτερικό περιηγητή", "assistant.search.placeholder": "Αναζήτηση", "deeply_thought": "Έχει βαθιά σκεφτεί (χρήση {{secounds}} δευτερόλεπτα)", "default.description": "Γεια σου, εί<PERSON><PERSON><PERSON> ο προεπαγγελμα<PERSON>ι<PERSON><PERSON>ς βοηθός. Μπορείς να ξεκινήσεις να μου μιλάς αμέσως.", "default.name": "Προεπαγγ<PERSON>λ<PERSON>α<PERSON><PERSON><PERSON><PERSON><PERSON> βοηθός", "default.topic.name": "Προεπαγγελματικ<PERSON>ς θέμα", "history": {"assistant_node": "<PERSON><PERSON><PERSON><PERSON><PERSON>ς", "click_to_navigate": "Κάντε κλικ για να μεταβείτε στο αντίστοιχο μήνυμα", "coming_soon": "Το διάγραμμα ροής συνομιλίας θα είναι σύντομα διαθέσιμο", "no_messages": "Δεν βρέθηκαν μηνύματα", "start_conversation": "Ξεκινήστε μια συνομιλία για να δείτε το διάγραμμα ροής", "title": "Ιστορικ<PERSON> συνομιλιών", "user_node": "<PERSON>ρή<PERSON><PERSON><PERSON>ς", "view_full_content": "Προβολή πλήρους περιεχομένου"}, "input.auto_resize": "Αυτόματη μείωση ύψους", "input.clear": "Καθα<PERSON>ισμ<PERSON>ς μηνυμάτων {{Command}}", "input.clear.content": "Είσαι σίγουρος ότι θέλεις να διαγράψεις όλα τα μηνύματα της τρέχουσας συζήτησης;", "input.clear.title": "Καθαρισμός μηνυμάτων", "input.collapse": "Συμπιέζω", "input.context_count.tip": "Πλήθος ενδιάμεσων/Μέγιστο πλήθος ενδιάμεσων", "input.estimated_tokens.tip": "Εκτιμώμενος αριθμός tokens", "input.expand": "Επεκτάση", "input.file_not_supported": "Το μοντέλο δεν υποστηρίζει αυτό το είδος αρχείων", "input.generate_image": "Δημιουργ<PERSON>α εικόνας", "input.generate_image_not_supported": "Το μοντέλο δεν υποστηρίζει τη δημιουργία εικόνων", "input.knowledge_base": "Βάση γνώσεων", "input.new.context": "Καθα<PERSON>ισ<PERSON><PERSON>ς ενδιάμεσων {{Command}}", "input.new_topic": "Νέο θέμα {{Command}}", "input.pause": "Παύση", "input.placeholder": "Εισάγετε μήνυμα εδώ...", "input.send": "Αποστολή", "input.settings": "Ρυθμίσεις", "input.thinking": "Σκέψη", "input.thinking.budget_exceeds_max": "Ο προϋπολογισμός σκέψης υπερβαίνει τον μέγιστο αριθμό token", "input.thinking.mode.custom": "Προσαρμοσμένο", "input.thinking.mode.custom.tip": "Ο μέγιστος αριθμός token που μπορεί να σκεφτεί το μοντέλο. Πρέπει να ληφθεί υπόψη το όριο πλαισίου του μοντέλου, διαφορετικά θα εμφανιστεί σφάλμα", "input.thinking.mode.default": "Προεπιλογή", "input.thinking.mode.default.tip": "Το μοντέλο θα αποφασίσει αυτόματα τον αριθμό token για σκέψη", "input.topics": "Θέματα", "input.translate": "Μετάφραση στο {{target_language}}", "input.translating": "Μετάφραση...", "input.upload": "Φόρτωση εικόνας ή έγγραφου", "input.upload.document": "Φόρτωση έγγραφου (το μοντέλ<PERSON> δεν υποστηρίζει εικόνες)", "input.upload.upload_from_local": "Μεταφόρτωση αρχείου από τον υπολογιστή...", "input.web_search": "Ενεργοποίηση διαδικτυα<PERSON><PERSON>ς αναζήτησης", "input.web_search.builtin": "Ενσωματωμένη στο μοντέλο", "input.web_search.builtin.disabled_content": "Η τρέχουσα έκδοση του μοντέλου δεν υποστηρίζει τη δυνατότητα διαδικτυακής αναζήτησης", "input.web_search.builtin.enabled_content": "Χρήση της ενσωματωμένης δυνατότητας διαδικτυακής αναζήτησης του μοντέλου", "input.web_search.button.ok": "Πήγαινε στις ρυθμίσεις", "input.web_search.enable": "Ενεργοποίηση διαδικτυα<PERSON><PERSON>ς αναζήτησης", "input.web_search.enable_content": "Πρέπει να ελέγξετε τη σύνδεση με το διαδίκτυο στις ρυθμίσεις πρώτα", "input.web_search.no_web_search": "<PERSON><PERSON><PERSON><PERSON><PERSON> διαδίκτυο", "input.web_search.no_web_search.description": "Να μην ενεργοποιηθεί η δυνατότητα διαδικτυακής αναζήτησης", "message.new.branch": "Διακοπή", "message.new.branch.created": "Νέα διακοπή δημιουργήθηκε", "message.new.context": "Καθαρισμ<PERSON>ς ενδιάμεσων", "message.quote": "Αναφορά", "message.regenerate.model": "Εναλλαγ<PERSON> μοντέλου", "message.useful": "Χρήσιμ<PERSON>", "navigation": {"bottom": "Επιστροφή στο κάτω μέρος", "close": "Κλείσιμο", "first": "Ήδη το πρώτο μήνυμα", "history": "Ιστορικό συνομιλίας", "last": "Ήδη το τελευταίο μήνυμα", "next": "Επόμενο μήνυμα", "prev": "Προηγούμενο μήνυμα", "top": "Επιστροφή στην κορυφή"}, "resend": "Ξαναστείλε", "save": "Αποθήκευση", "settings.code_cache_max_size": "Όριο κρυφής μνήμης", "settings.code_cache_max_size.tip": "Μέγιστος αριθμός χαρακτήρων (σε χιλιάδες) που επιτρέπεται να αποθηκευτούν στην κρυφή μνήμη, υπολογ<PERSON>ζεται με βάση τον κώδικα με χρώματα. Ο κώδικας με χρώματα είναι πολύ πιο μακρύς από τον καθαρό κείμενο.", "settings.code_cache_threshold": "Κατώφλι κρυφής μνήμης", "settings.code_cache_threshold.tip": "Ελάχιστο μήκος κώδικα (σε χιλιάδες χαρακτήρες) που επιτρέπεται να αποθηκευτεί στην κρυφή μνήμη. Μόνο τα τμήματα που υπερβαίνουν το κατώφλι θα αποθηκεύονται στην κρυφή μνήμη", "settings.code_cache_ttl": "Διάρκεια κρυφής μνήμης", "settings.code_cache_ttl.tip": "<PERSON>ρ<PERSON><PERSON><PERSON> λήξης της κρυφής μνήμης (σε λεπτά)", "settings.code_cacheable": "Κρυφή μνήμη κώδικα", "settings.code_cacheable.tip": "Η κρυφή μνήμη των τμημάτων κώδικα μπορεί να μειώσει τον χρόνο απεικόνισης μεγάλων τμημάτων κώδικα, αλ<PERSON><PERSON> αυξάνει τη χρήση μνήμης", "settings.code_collapsible": "Οι κώδικες μπορούν να συμπιεζόνται", "settings.code_wrappable": "Οι κώδικες μπορούν να γράφονται σε διαφορετική γραμμή", "settings.context_count": "<PERSON><PERSON><PERSON><PERSON><PERSON> ενδιάμεσων", "settings.context_count.tip": "Πλήθος των μηνυμάτων που θα παραμείνουν στα ενδιάμεσα, ό<PERSON><PERSON> μεγαλύτερο είναι το αριθμός, τόσ<PERSON> μεγαλύτερο είναι το μήκος του ενδιάμεσου και τόσο περισσότερα tokens χρησιμοποιούνται. Συνομιλία συνήθως συνιστάται μεταξύ 5-10", "settings.max": "Όχι ορισμένο", "settings.max_tokens": "Ενεργοποίηση περιορισμού μήκους μηνύματος", "settings.max_tokens.confirm": "Ενεργοποίηση περιορισμού μήκους μηνύματος", "settings.max_tokens.confirm_content": "Μετά την ενεργοποίηση του περιορισμού μήκους μηνύματος, ο μέγιστος αριθμός των tokens που χρησιμοποιούνται κάθε φορά, θα επηρεάζει το μήκος της απάντησης. Πρέπει να το ρυθμίζετε βάσει των περιορισμών του πλαισίου του μοντέλου, διαφορετικά θα σφάλλεται.", "settings.max_tokens.tip": "Ο μέγιστος αριθμός των tokens που χρησιμοποιούνται κάθε φορά, θα επηρεάζει το μήκος της απάντησης. Πρέπει να το ρυθμίζετε βάσει των περιορισμών του πλαισίου του μοντέλου, διαφορετικά θα σφάλλεται.", "settings.reset": "Επαναφορά", "settings.set_as_default": "Εφαρμογή στον προεπαγγελματικό βοηθό", "settings.show_line_numbers": "Εμφάνιση αριθμού γραμμών στον κώδικα", "settings.temperature": "Θερμοκρασία μοντέλου", "settings.temperature.tip": "Ο αντικειμενικ<PERSON>ς βαθμός τυχαιότητας του μοντέλου στην παραγωγή κειμένου. Ο μεγαλύτερος αριθμός σημαίνει περισσότερη ποικιλία, δημιουργικότητα και τυχαιότητα στις απαντήσεις· η έδρα μετά την επιλογή 0 επιστρέφει απαντήσεις βάσει των γεγονότων. Για καθημερινές συζητήσεις προτείνεται η επιλογή 0.7.", "settings.thought_auto_collapse": "Αυτόματη συμπίεση σκέψεων", "settings.thought_auto_collapse.tip": "Μετά τη λήξη της σκέψης, η σκέψη αυτόματα συμπιεζεται", "settings.top_p": "Top-P", "settings.top_p.tip": "Η προεπιλογή είναι 1, <PERSON><PERSON><PERSON> μικρ<PERSON><PERSON><PERSON>ρος είναι ο αριθμός, τό<PERSON><PERSON> μικρότερη είναι η ποικιλία του περιεχομένου που παράγεται από το AI και τόσο εύκολοτερο είναι να κατανοείται· όσο μεγαλύτερος είναι, τόσο μεγαλύτερη είναι η ποικιλία των λέξεων που μπορεί να χρησιμοποιήσει το AI.", "suggestions.title": "Προτεινόμενες ερωτήσεις", "thinking": "Σκέψη", "topics.auto_rename": "Δημιουργ<PERSON>α θέματος", "topics.clear.title": "Καθαρισμός μηνυμάτων", "topics.copy.image": "Αντιγραφή ως εικόνα", "topics.copy.md": "Αντιγραφή ως <PERSON>", "topics.copy.plain_text": "Αντιγρα<PERSON><PERSON> ως απλό κείμενο (αφαίρεση Markdown)", "topics.copy.title": "Αντιγραφή", "topics.delete.shortcut": "Πατήστε {{key}} για να διαγράψετε αμέσως", "topics.edit.placeholder": "Εισαγάγετε το νέο όνομα", "topics.edit.title": "Επεξεργα<PERSON><PERSON><PERSON> ονόματος θέματος", "topics.export.image": "Εξαγωγή ως εικόνα", "topics.export.joplin": "Εξαγωγ<PERSON> στο <PERSON>", "topics.export.md": "Εξαγωγή ως <PERSON>", "topics.export.md.reason": "Εξαγωγή σε Markdown (περιλαμβανομένης της σκέψης)", "topics.export.notion": "Εξαγωγή στο Notion", "topics.export.obsidian": "Εξαγωγή στο Obsidian", "topics.export.obsidian_atributes": "Ρυθμίσεις σημείου σημείωσης", "topics.export.obsidian_btn": "ΟΚ", "topics.export.obsidian_created": "Ημερομηνία δημιουργίας", "topics.export.obsidian_created_placeholder": "Επιλέξτε την ημερομηνία δημιουργίας", "topics.export.obsidian_export_failed": "Η εξαγωγή απέτυχε", "topics.export.obsidian_export_success": "Η εξαγωγή ήταν επιτυχής", "topics.export.obsidian_fetch_error": "Αποτυχία λήψης της αποθήκης Obsidian", "topics.export.obsidian_fetch_folders_error": "Αποτυχ<PERSON>α λήψης της δομής φακέλων", "topics.export.obsidian_loading": "Φόρτωση...", "topics.export.obsidian_no_vault_selected": "Παρ<PERSON><PERSON><PERSON><PERSON><PERSON> επιλέξτε μια αποθήκη πρώτα", "topics.export.obsidian_no_vaults": "Δεν βρέθηκε αποθήκη Obsidian", "topics.export.obsidian_operate": "Επεξεργα<PERSON><PERSON>α μεθόδου", "topics.export.obsidian_operate_append": "Επισυναγωγή", "topics.export.obsidian_operate_new_or_overwrite": "Νέ<PERSON> (επιστροφή σε επιστροφή)", "topics.export.obsidian_operate_placeholder": "Επιλέξτε την μεθόδο επεξεργασίας", "topics.export.obsidian_operate_prepend": "Προσθήκη", "topics.export.obsidian_path": "Διαδρομή", "topics.export.obsidian_path_placeholder": "Επιλέξτε διαδρομή", "topics.export.obsidian_root_directory": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> κατάλογος", "topics.export.obsidian_select_vault_first": "Πα<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> επιλέξτε πρώτα μια αποθήκη", "topics.export.obsidian_source": "Πηγή", "topics.export.obsidian_source_placeholder": "Εισάγετε την πηγή", "topics.export.obsidian_tags": "Ετικέτες", "topics.export.obsidian_tags_placeholder": "Εισάγετε τις ετικέτες, χωρισ<PERSON>ένες με κόμματα στα Αγγλικά, τα ετικέτα μπορεί να μην είναι μόνο αριθμοί", "topics.export.obsidian_title": "Τίτλος", "topics.export.obsidian_title_placeholder": "Εισάγετε τον τίτλο", "topics.export.obsidian_title_required": "Ο τίτλος δεν μπορεί να είναι κενός", "topics.export.obsidian_vault": "Αποθήκη Obsidian", "topics.export.obsidian_vault_placeholder": "Επιλέξτε το όνομα της αποθήκης", "topics.export.siyuan": "Εξαγωγή στο <PERSON>pad", "topics.export.title": "Εξαγωγή", "topics.export.title_naming_failed": "Η δημιουργία του τίτλου απέτυχε, θα χρησιμοποιηθεί ο προεπιλεγμένος τίτλος", "topics.export.title_naming_success": "Ο τίτλος δημιουργήθηκε επιτυχώς", "topics.export.wait_for_title_naming": "Γενικευμένος τίτλος...", "topics.export.word": "Εξαγωγή ως Word", "topics.export.yuque": "Εξαγωγή στο Yuque", "topics.list": "Λίστα θεμάτων", "topics.move_to": "Μετακίνηση στο", "topics.new": "Ξεκινήστε νέα συζήτηση", "topics.pinned": "Σταθερά θέματα", "topics.prompt": "Προ<PERSON><PERSON><PERSON>κώμενα όρια", "topics.prompt.edit.title": "Επεξεργασία προσδοκώμενων όριων", "topics.prompt.tips": "Προσδοκώμενα όρια: προσθέτει επιπλέον επιστημονικές προσθήκες για το παρόν θέμα", "topics.title": "Θέματα", "topics.unpinned": "Αποστέλλω", "translate": "Μετάφραση"}, "html_artifacts": {"code": "Κώδικας", "generating": "Δημιουργία", "preview": "Προεπισκόπηση", "split": "Διαχωρισμ<PERSON>ς"}, "code_block": {"collapse": "συμπεριληφθείς", "disable_wrap": "ακύρωση αλλαγής γραμμής", "enable_wrap": "άλλαγη γραμμής", "expand": "επιλογή"}, "common": {"add": "Προσθέστε", "advanced_settings": "Προχωρημένες ρυθμίσεις", "and": "και", "assistant": "Εξυπνιασμένη Ενότητα", "avatar": "Εικονίδιο", "back": "Πίσω", "cancel": "Άκυρο", "chat": "Συζήτηση", "clear": "Καθαρισμός", "close": "Κλείσιμο", "collapse": "Σύμπτυξη", "confirm": "Επιβεβαίωση", "copied": "Αντιγράφηκε", "copy": "Αντιγραφή", "cut": "Κοπή", "default": "Προεπιλογή", "delete": "Διαγραφή", "description": "Περιγραφή", "docs": "Έγγραφα", "download": "Λή<PERSON>η", "duplicate": "Αντιγραφή", "edit": "Επεξεργασία", "expand": "Επεκτάση", "footnote": "Παραπομπή", "footnotes": "Παραπομπ<PERSON>ς", "fullscreen": "Εισήχθη σε πλήρη οθόνη, πατήστε F11 για να έξω", "inspect": "Επιθεώρηση", "knowledge_base": "Βάση Γνώσεων", "language": "Γλώσσα", "loading": "Φόρτωση...", "model": "<PERSON>ο<PERSON><PERSON><PERSON><PERSON><PERSON>", "models": "Μοντ<PERSON>λα", "more": "Περισσότερα", "name": "Όνομα", "paste": "Επικόλληση", "prompt": "Ενδεικτικός ρήματος", "provider": "Παρέχων", "reasoning_content": "Έχει σκεφτεί πολύ καλά", "regenerate": "Ξαναπαραγωγή", "rename": "Μετονομασία", "reset": "Επαναφορά", "save": "Αποθήκευση", "search": "Αναζήτηση", "select": "Επιλογή", "sort": {"pinyin": "Ταξινόμηση κατά Πινγίν", "pinyin.asc": "Αύξουσα ταξινόμηση κατά Πινγίν", "pinyin.desc": "Φθίνουσα ταξινόμηση κατά Πινγίν"}, "topics": "Θέματα", "warning": "Προσοχή", "you": "Εσ<PERSON><PERSON>ς"}, "docs": {"title": "Βοήθεια"}, "error": {"backup.file_format": "<PERSON><PERSON><PERSON><PERSON> μορφή αρχείου που επιστρέφεται", "chat.response": "Σφάλμα. <PERSON><PERSON><PERSON> δεν έχετε ρυθμίσει το κλειδί API, πηγαίνετε στο ρυθμισμένα > παρέχοντας το πρόσωπο του μοντέλου", "http": {"400": "Σφάλμα ζητήματος, π<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ελέγξτε αν τα παράμετρα του ζητήματος είναι σωστά. Εάν έχετε αλλάξει τις ρυθμίσεις του μοντέλου, επαναφέρετε τις προεπιλεγμένες ρυθμίσεις.", "401": "Αποτυχία επιβεβαίωσης ταυτότητας, παρα<PERSON><PERSON><PERSON><PERSON> ελέγξτε αν η κλειδί API είναι σωστή", "403": "Απαγορ<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> η πρόσβαση, π<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> μεταφράστε το συγκεκριμένο σφάλμα για να δείτε την αιτία ή επικοινωνήστε με τον παροχεύτη για να μάθετε την αιτία της απαγόρευσης", "404": "Το μοντέλο δεν υπάρχει ή η διαδρομή παραγγελίας είναι λάθος", "429": "Υπερβολική συχνότητα ζητημάτων, παρα<PERSON><PERSON><PERSON><PERSON> δοκιμάστε ξανά", "500": "Εσωτερι<PERSON><PERSON> σφάλμα διαχειριστή, παρακαλ<PERSON> δοκιμάστε ξανά", "502": "Σφάλμ<PERSON> φάρων, πα<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> δοκιμάστε ξανά", "503": "Η υπηρεσία δεν είναι διαθέσιμη, παρα<PERSON><PERSON><PERSON><PERSON> δοκιμάστε ξανά", "504": "Υπερχρονισμός φάρων, παρα<PERSON><PERSON>λ<PERSON> δοκιμάστε ξανά"}, "model.exists": "Το μοντέλο υπ<PERSON><PERSON><PERSON><PERSON>ι ήδη", "no_api_key": "Δεν έχετε ρυθμίσει το κλειδί API", "pause_placeholder": "Διακόπηκε", "provider_disabled": "Ο παρεχόμενος παροχός του μοντέλου δεν είναι ενεργοποιημένος", "render": {"description": "Απέτυχε η ώθηση της εξίσωσης, πα<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ελέγξτε το σωστό μορφάτι της", "title": "Σφάλμα Παρασκήνιου"}, "unknown": "Άγνωστο σφάλμα", "user_message_not_found": "Αδυναμ<PERSON>α εύρεσης της αρχικής μηνύματος χρήστη"}, "export": {"assistant": "βοηθός", "attached_files": "συνημμένα αρχεία", "conversation_details": "λεπτομέρειες συζήτησης", "conversation_history": "Ιστορικ<PERSON> Συζητήσεων", "created": "Ημερομηνία Δημιουργίας", "last_updated": "Τελευτα<PERSON>α ενημέρωση", "messages": "Αριθμός Μηνυμάτων", "user": "<PERSON>ρή<PERSON><PERSON><PERSON>ς"}, "files": {"actions": "Ενέργειες", "all": "Όλα τα αρχεία", "count": "Αριθμός αρχείων", "created_at": "Ημερομηνία δημιουργίας", "delete": "Διαγραφή", "delete.content": "Η διαγραφή του αρχείου θα διαγράψει την αναφορά του σε όλα τα μηνύματα. Είστε σίγουροι ότι θέλετε να διαγράψετε αυτό το αρχείο;", "delete.paintings.warning": "Το σχεδίο περιλαμβάνει αυτή την εικόνα και δεν είναι παρόλως δυνατή η διαγραφή.", "delete.title": "Διαγρα<PERSON>ή αρχείου", "document": "Έγγραφο", "edit": "Επεξεργασία", "file": "Αρχείο", "image": "Εικόνα", "name": "Όνομα αρχείου", "open": "Άνοιγμα", "size": "Μέγεθος", "text": "Κείμενο", "title": "Αρχεία", "type": "Τύπος"}, "gpustack": {"keep_alive_time.description": "<PERSON>ρ<PERSON><PERSON><PERSON> που ο μοντ<PERSON><PERSON>ος παραμένει στη μνήμη (προεπιλογή: 5 λεπτά)", "keep_alive_time.placeholder": "λεπτά", "keep_alive_time.title": "<PERSON><PERSON><PERSON><PERSON><PERSON> διατήρη<PERSON>ης ενεργοποίησης", "title": "GPUStack"}, "history": {"continue_chat": "Συνεχίστε το συνομιλημένο", "locate.message": "Εφαρμογή στο μήνυμα", "search.messages": "Αναζήτη<PERSON>η όλων των μηνυμάτων", "search.placeholder": "Αναζήτηση θεμάτων ή μηνυμάτων...", "search.topics.empty": "Δεν βρέθηκαν σχετικά θέματα, πατήστε Enter για να αναζητήσετε όλα τα μηνύματα", "title": "Αναζήτηση θεμάτων"}, "knowledge": {"add": {"title": "Προσθήκη βιβλιοθήκης γνώσεων"}, "add_directory": "Προσθήκη καταλόγου", "add_file": "Προσθήκη αρχείου", "add_note": "Προσθήκη σημειώματος", "add_sitemap": "Χ<PERSON>ρτης τόπων", "add_url": "Προσθήκη διευθύνσεως", "cancel_index": "Άκυρη ευρετήριοποίηση", "chunk_overlap": "Μέγ<PERSON><PERSON>ος επιφάνειας", "chunk_overlap_placeholder": "Προεπιλογή (δεν συνιστάται να το αλλάξετε)", "chunk_overlap_tooltip": "Το ποσοστό επιφάνειας επιφάνειας μεταξύ γειτνιώντων κειμένων μπλοκ, για να εξασφαλίσετε ότι τα κείμενα μπλοκ μετά τη διακοσμηση εξακολουθούν να έχουν σχέση σε προσδιορισμό, βελτιώνοντας την συνολική αποτελεσματικότητα επεξεργασίας με μοντέλα μεγάλου κειμένου", "chunk_size": "Μέγ<PERSON><PERSON>ος μερισμού", "chunk_size_change_warning": "Η αλλαγή του μεγέθους μερισμού και της επιφάνειας επιφάνειας εφαρμόζεται μόνο για νέα προσθέτομεν αρχεία", "chunk_size_placeholder": "Προεπιλογή (δεν συνιστάται να το αλλάξετε)", "chunk_size_too_large": "Το μέγεθος μερισμού δεν μπορεί να ξεπεράσει το όριο πλάτους επιρροής του μοντέλου ({{max_context}})", "chunk_size_tooltip": "Διαχωρισμός των έγγραφων σε μεριδισμούς, με το μέγεθος κάθε μεριδισμού να μην ξεπεράζει το όριο πλάτους επιρροής του μοντέλου", "clear_selection": "Καθαρισ<PERSON><PERSON>ς επιλογής", "delete": "Διαγραφή", "delete_confirm": "Είστε σίγου<PERSON>ος ότι θέλετε να διαγράψετε αυτή τη βάση γνώσεων;", "dimensions": "Διαστ<PERSON><PERSON><PERSON><PERSON>ς ενσωμάτωσης", "dimensions_auto_set": "Αυτόματη ρύθμιση διαστάσεων ενσωμάτωσης", "dimensions_default": "Το μοντέλο θα χρησιμοποιήσει τις προεπιλεγμένες διαστάσεις ενσωμάτωσης", "dimensions_error_invalid": "Παρ<PERSON><PERSON><PERSON><PERSON><PERSON> εισάγετε μέγεθος διαστάσεων ενσωμάτωσης", "dimensions_set_right": "⚠️ Βεβαιωθείτε ότι το μοντέλο υποστηρίζει το καθορισμένο μέγεθος διαστάσεων ενσωμάτωσης", "dimensions_size_placeholder": " Μέγεθος διαστά<PERSON>εων ενσωμάτωσης, π.χ. 1024", "dimensions_size_too_large": "Οι διαστάσεις ενσωμάτωσης δεν μπορούν να υπερβούν το όριο περιεχομένου του μοντέλου ({{max_context}})", "dimensions_size_tooltip": "Το μέγεθος των διαστάσεων ενσωμάτωσης. Όσο μεγαλύτερη η τιμή, τόσο περισσότερες οι διαστάσεις ενσωμάτωσης, α<PERSON><PERSON><PERSON> και οι απαιτούμενες μονάδες (<PERSON><PERSON><PERSON>).", "directories": "Κατάλογοι", "directory_placeholder": "Εισάγετε το δρομολόγιο του καταλόγου", "document_count": "Ποσότητα κειμένων που ζητούνται", "document_count_default": "Προεπιλογή", "document_count_help": "Όσο μεγαλύτερη είναι η ποσότητα των κειμένων που ζητούνται, τόσο περισσότερες πληροφορίες παρέχονται, αλ<PERSON><PERSON> και οι καταναλωτικ<PERSON><PERSON> Token επειδή περισσότερα", "drag_file": "Βάλτε το αρχείο εδώ", "edit_remark": "Μεταβολή σημειώματος", "edit_remark_placeholder": "Εισάγετε το σημείωμα", "empty": "Λεηλα<PERSON><PERSON><PERSON> βάσης γνώσεων", "file_hint": "Υποστηρίζεται το {{file_types}} μορφάττων", "index_all": "Ευρετήριοποίηση όλων", "index_cancelled": "Η ευρετήριοποίηση διακόπηκε", "index_started": "Η ευρετήριοποίηση ξεκίνησε", "invalid_url": "Μη έγκυρη διευθύνση", "model_info": "Πληροφορ<PERSON>ες μοντέλου", "no_bases": "Λεηλα<PERSON><PERSON><PERSON> βάσης γνώσεων", "no_match": "Δεν βρέθηκαν στοιχεία γνώσεων", "no_provider": "Η παροχή υπηρεσιών μοντέλου βάσης γνώσεων χαθηκε, αυτή η βάση γνώσεων δεν θα υποστηρίζεται πλέον, παρα<PERSON><PERSON><PERSON>είστε να δημιουργήσετε ξανά μια βάση γνώσεων", "not_set": "Δεν έχει ρυθμιστεί", "not_support": "Το μοντέλ<PERSON> βάσης γνώσεων έχει ενημερωθεί, αυτή η βάση γνώσεων δεν θα υποστηρίζεται πλέον, παρακα<PERSON>είστε να δημιουργήσετε ξανά μια βάση γνώσεων", "notes": "Σημειώματα", "notes_placeholder": "Εισάγετε πρόσθετες πληροφορίες ή πληροφορίες προσδιορισμού για αυτή τη βάση γνώσεων...", "rename": "Μετονομασία", "search": "Αναζήτηση βάσης γνώσεων", "search_placeholder": "Εισάγετε την αναζήτηση", "settings": "Ρυθμίσεις βάσης γνώσεων", "sitemap_placeholder": "Εισάγετε τη διεύθυνση URL του χάρτη τόπων", "sitemaps": "Στοιχεία του δικτύου", "source": "Πηγή", "status": "Κατάσταση", "status_completed": "Ολοκληρώθηκε", "status_failed": "Αποτυχία", "status_new": "Προστέθηκε", "status_pending": "Εκκρεμής", "status_processing": "Επεξεργασία", "threshold": "Περιθώριο συνάφειας", "threshold_placeholder": "Δεν έχει ρυθμιστεί", "threshold_too_large_or_small": "Το περιθώριο δεν μπορεί να είναι μεγαλύτερο από 1 ή μικρότερο από 0", "threshold_tooltip": "Χρησιμοποιείται για τη μετρηση της σχέσης συνάφειας μεταξύ της ερώτησης του χρήστη και των περιεχομένων της βάσης γνώσεων (0-1)", "title": "Βάση γνώσεων", "topN": "Ποσότητα αποτελεσμάτων που επιστρέφονται", "topN__too_large_or_small": "Η ποσότητα των αποτελεσμάτων που επιστρέφονται δεν μπορεί να είναι μεγαλύτερη από 100 ή μικρότερη από 1", "topN_placeholder": "Δεν έχει ρυθμιστεί", "topN_tooltip": "Η ποσότητα των επιστρεφόμενων αποτελεσμάτων που συνάφονται, όσ<PERSON> μεγαλύτερη είναι η τιμή, τόσο περισσότερα αποτελέσματα συνδέονται, αλλά και οι καταναλωτικοί Token επειδή περισσότερα", "url_added": "Η διεύθυνση προστέθηκε", "url_placeholder": "Εισάγετε τη διεύθυνση, χωρ<PERSON>στε πολλαπλές διευθύνσεις με επιστροφή", "urls": "Διευθύνσεις"}, "languages": {"arabic": "Αραβικά", "chinese": "Σίναρα Κινέζικά", "chinese-traditional": "Παραδοσιακά Κινέζικά", "english": "Αγγλικ<PERSON>", "french": "Γαλλικά", "german": "Γερμανικά", "italian": "Ιταλικά", "japanese": "Ιαπωνικά", "korean": "Κορεά<PERSON>ικά", "portuguese": "Πορτογαλικά", "russian": "Ρωσικά", "spanish": "Ισπανικά"}, "lmstudio": {"keep_alive_time.description": "<PERSON>ρ<PERSON><PERSON><PERSON> που ο μοντέ<PERSON>ος διατηρείται στη μνήμη μετά από το συνομιλητή (προεπιλογή: 5 λεπτά)", "keep_alive_time.placeholder": "λεπτά", "keep_alive_time.title": "<PERSON><PERSON><PERSON><PERSON><PERSON> διατήρη<PERSON>ης ενεργοποίησης", "title": "LM Studio"}, "mermaid": {"download": {"png": "Λήψη PNG", "svg": "Λήψη SVG"}, "resize": {"zoom-in": "Μεγάλυνση", "zoom-out": "Σμικρύνση"}, "tabs": {"preview": "Προεπισκόπηση", "source": "Κώδικ<PERSON>ς πηγής"}, "title": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "message": {"agents": {"import.error": "Η εισαγωγή απέτυχε", "imported": "Εισήχθη επιτυχώς"}, "api.check.model.title": "Επιλέξτε το μοντέλο που θα ελέγξετε", "api.connection.failed": "Η σύνδεση απέτυχε", "api.connection.success": "Η σύνδεση ήταν επιτυχής", "assistant.added.content": "Ο ενεργοπο<PERSON>η<PERSON><PERSON><PERSON>ος αστρόναυτης προστέθηκε επιτυχώς", "attachments": {"pasted_image": "Εικόνα στο πινάκιδα", "pasted_text": "Κείμενο στο πινάκιδα"}, "backup.failed": "Η αντιγραφή ασφαλείας απέτυχε", "backup.start.success": "Η αρχή της αντιγραφής ασφαλε<PERSON>ας ήταν επιτυχής", "backup.success": "Η αντιγραφή ασφαλε<PERSON>ας ήταν επιτυχής", "chat.completion.paused": "Η συζήτηση διακόπηκε", "citation": "{{count}} αναφορές", "citations": "Περιεχόμενα αναφοράς", "copied": "Αντιγράφηκε", "copy.failed": "Η αντιγραφή απέτυχε", "copy.success": "Η αντιγραφή ήταν επιτυχής", "download.failed": "Αποτυχία λήψης", "download.success": "Λήψη ολοκληρώθηκε", "error.chunk_overlap_too_large": "Η επικάλυψη μεριδίων δεν μπορεί να είναι μεγαλύτερη από το μέγεθος του μεριδίου", "error.dimension_too_large": "Το μέγεθος του περιεχομένου είναι πολύ μεγάλο", "error.enter.api.host": "Παρα<PERSON><PERSON><PERSON><PERSON> εισάγετε τη διεύθυνση API σας", "error.enter.api.key": "Παρα<PERSON><PERSON><PERSON><PERSON> εισάγετε το κλειδί API σας", "error.enter.model": "Παρα<PERSON><PERSON><PERSON><PERSON> επιλέξτε ένα μοντέλο", "error.enter.name": "Π<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> εισάγετε ένα όνομα για τη βάση γνώσεων", "error.get_embedding_dimensions": "Απέτυχε η πρόσληψη διαστάσεων ενσωμάτωσης", "error.invalid.api.host": "Μη έγκυρη διεύθυνση API", "error.invalid.api.key": "Μη έγκυρο κλειδί API", "error.invalid.enter.model": "Παρα<PERSON><PERSON><PERSON><PERSON> επιλέξτε ένα μοντέλο", "error.invalid.nutstore": "Μη έγκυρη ρύθμιση Nutstore", "error.invalid.nutstore_token": "Μη έγκυρο Token Nutstore", "error.invalid.proxy.url": "Μη έγκυρη διεύθυνση προξενικού", "error.invalid.webdav": "Μη έγκυρη ρύθμιση WebDAV", "error.joplin.export": "Η εξαγωγή του <PERSON><PERSON><PERSON> απέτυχε, παρα<PERSON><PERSON><PERSON><PERSON>ίστε να ελέγξετε τη σύνδεση και τη διαμόρφωση κατά τη διατύπωση του χειρισμού", "error.joplin.no_config": "Δεν έχετε διαθέσιμο το Token εξουσιοδότησης του <PERSON><PERSON><PERSON> ή το URL του <PERSON><PERSON><PERSON>", "error.markdown.export.preconf": "Η εξαγωγή αρχείου Markdown στο προϋπολογισμένο μοντέλο απέτυχε", "error.markdown.export.specified": "Η εξαγωγή αρχείου Markdown απέτυχε", "error.notion.export": "Σφάλμα στην εξαγωγή του Notion, παρακαλείστε να ελέγξετε τη σύνδεση και τη διαμόρφωση κατά τη διατύπωση του χειρισμού", "error.notion.no_api_key": "Δεν έχετε διαθέσιμο το API Key του Notion ή το ID της βάσης του Notion", "error.siyuan.export": "Η έκθεση σημειώσεων <PERSON> απέτυχε, ελέγξτε την κατάσταση σύνδεσης και τις ρυθμίσεις σύμφωνα με τα έγγραφα", "error.siyuan.no_config": "Δεν έχει ρυθμιστεί η διεύθυνση API ή το Token του Siyuan Notes", "error.yuque.export": "Σφάλμα στην εξαγωγή της <PERSON>, παρακαλείστε να ελέγξετε τη σύνδεση και τη διαμόρφωση κατά τη διατύπωση του χειρισμού", "error.yuque.no_config": "Δεν έχετε διαθέσιμο το Token της Yu<PERSON> ή το URL της βάσης της <PERSON>que", "group.delete.content": "Η διαγραφή της ομάδας θα διαγράψει τις ερωτήσεις των χρηστών και όλες τις απαντήσεις του αστρόναυτη", "group.delete.title": "Διαγρα<PERSON><PERSON> ομάδας", "ignore.knowledge.base": "Λειτουργ<PERSON><PERSON> σύνδεσης ενεργοποιημένη, αγνοείται η βάση γνώσεων", "info.notion.block_reach_limit": "Η συζήτηση είναι πολύ μεγάλη, εξάγεται σε περιοχές στο Notion", "loading.notion.exporting_progress": "Εξάγεται στο Notion ({{current}}/{{total}})...", "loading.notion.preparing": "Ετοιμάζεται η εξαγωγή στο Notion...", "mention.title": "Εναλλαγ<PERSON> απάντη<PERSON>ης αστρόναυτη", "message.code_style": "Στυλ κώδικα", "message.delete.content": "Θέλετε να διαγράψετε αυτό το μήνυμα;", "message.delete.title": "Διαγρα<PERSON><PERSON> μηνύματος", "message.multi_model_style": "Στυλ πολλα<PERSON>λών απαντή<PERSON>εων μοντέλου", "message.multi_model_style.fold": "Κατάσταση ενσωμάτωσης", "message.multi_model_style.fold.compress": "Εναλλαγή στη συμπιεσμένη διάταξη", "message.multi_model_style.fold.expand": "Εναλλαγή στην επεκτατική διάταξη", "message.multi_model_style.grid": "Διάταξη κάρτας", "message.multi_model_style.horizontal": "Διάταξη επίπεδης", "message.multi_model_style.vertical": "Διάταξη κάθετης", "message.style": "Στυλ μηνύματος", "message.style.bubble": "Αερογεύματα", "message.style.plain": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "processing": "Επεξεργασία...", "regenerate.confirm": "Η επαναδημιουργία θα αφαιρέσει το τρέχον μήνυμα", "reset.confirm.content": "Θέλετε να επαναφέρετε όλα τα δεδομένα;", "reset.double.confirm.content": "Όλα τα δεδομένα σας θα χαθούν, ε<PERSON>ν δεν έχετε κάνει αντιγραφή, δεν θα μπορείτε να ανακτήσετε τα δεδομένα, είστε σίγουροι ότι θέλετε να συνεχίσετε;", "reset.double.confirm.title": "Η απώλεια δεδομένων!!!", "restore.failed": "Η αποκατάσταση απέτυχε", "restore.success": "Η αποκατάστα<PERSON>η ήταν επιτυχής", "save.success.title": "Η αποθήκευση ήταν επιτυχής", "searching": "Ενεργοποιείται αναζήτηση στο διαδίκτυο...", "success.joplin.export": "Η εξαγωγή στο <PERSON>αν επιτυχής", "success.markdown.export.preconf": "Η εξαγωγή αρχείου Markdown στο προϋπολογισμένο μοντέλο ήταν επιτυχής", "success.markdown.export.specified": "Η εξαγωγή αρχείου Markdown ήταν επιτυχής", "success.notion.export": "Η εξαγωγή στο Notion ήταν επιτυχής", "success.siyuan.export": "Επιτυχής εξαγωγή στις σημειώσεις <PERSON>", "success.yuque.export": "Η εξαγωγή στη Yuque ήταν επιτυχής", "switch.disabled": "Παρακ<PERSON><PERSON><PERSON>ίστε να περιμένετε τη λήξη της τρέχουσας απάντησης", "tools": {"completed": "Ολοκληρώθηκε", "error": "Προέκυψε σφάλμα", "invoking": "κλήση σε εξέλιξη", "preview": "Προεπισκόπηση", "raw": "Ακα<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>το"}, "topic.added": "Η θεματική προστέθηκε επιτυχώς", "upgrade.success.button": "Επανεκκίνηση", "upgrade.success.content": "Επανεκκίνηση για να ολοκληρώσετε την ενημέρωση", "upgrade.success.title": "Η ενημέρωση ήταν επιτυχής", "warn.notion.exporting": "Εξαγωγή στο <PERSON>, μην επαναλάβετε την διαδικασία εξαγωγής!", "warn.siyuan.exporting": "Γίνεται εξαγωγή στις σημειώσεις <PERSON>· μην ξαναζητήσετε την έκθεση!", "warn.yuque.exporting": "Γίνεται έκθεση Yuque· μην ξαναζητήσετε την έκθεση!", "warning.rate.limit": "Υπερβολική συχνότητα στείλατε παρακ<PERSON>λ<PERSON> περιμένετε {{seconds}} δευτερόλεπτα και προσπαθήστε ξανά"}, "minapp": {"popup": {"close": "Κλείσιμο της εφαρμογής", "devtools": "Εργαλεία προγραμματιστή", "minimize": "Ελαχιστοποίηση της εφαρμογής", "open_link_external_off": "Τρέχον: Άνοιγμα συνδέσμου χρησιμοποιώντας το προεπιλεγμένο παράθυρο", "open_link_external_on": "Τρέχον: Άνοιγμα συνδέσμου στον περιηγητή", "openExternal": "Άνοιγμα στον περιηγητή", "refresh": "Ανανέωση", "rightclick_copyurl": "Αντιγραφή URL με δεξί κλικ"}, "sidebar": {"add": {"title": "Προσθήκη στην πλευρική μπάρα"}, "close": {"title": "Κλείσιμο"}, "closeall": {"title": "Κλείσιμο όλων"}, "hide": {"title": "Απόκρυψη"}, "remove": {"title": "Αφαίρεση από την πλευρική μπάρα"}, "remove_custom": {"title": "Διαγραφή προσαρμοσμένης εφαρμογής"}}, "title": "Μικρόπρογραμμα"}, "miniwindow": {"clipboard": {"empty": "Το πινάκιδα κόπων είναι άδειο"}, "feature": {"chat": "Απάντηση σ' αυτή την ερώτηση", "explanation": "Εξήγηση", "summary": "Σύνοψη", "translate": "Μετάφραση κειμένου"}, "footer": {"backspace_clear": "Πατήστε το πλήκτρο Backspace για να κάνετε εκκαθάριση", "copy_last_message": "Παράκαμε το τελευταίο μήνυμα", "esc": "πατήστε ESC για {{action}}", "esc_back": "Επιστροφή", "esc_close": "Κλείσιμο παραθύρου"}, "input": {"placeholder": {"empty": "Ρώτα τον {{model}} για βοήθεια...", "title": "Τι θέλεις να κάνεις με το κείμενο που είναι παρακάτω"}}, "tooltip": {"pin": "Καρφίτσωμα παραθύρου"}}, "models": {"add_parameter": "Προσθήκη παραμέτρων", "all": "Όλα", "custom_parameters": "Προσαρμοσμένοι παράμετροι", "dimensions": "{{dimensions}} διαστάσεις", "edit": "Επεξεργα<PERSON><PERSON>α μοντέλου", "embedding": "Ενσωμάτωση", "embedding_model": "Μοντ<PERSON><PERSON><PERSON> ενσωμάτωσης", "embedding_model_tooltip": "Κάντε κλικ στο κουμπί Διαχείριση στο παράθυρο Ρυθμίσεις -> Υπηρεσία Μοντέλων", "enable_tool_use": "Ενεργοποίηση κλήσης εργαλείου", "function_calling": "Ξεχωριστική Κλήση Συναρτήσεων", "no_matches": "Δεν υπάρχουν διαθέσιμα μοντέλα", "parameter_name": "Όνομα παραμέτρου", "parameter_type": {"boolean": "Πιθανότητα", "json": "JSON", "number": "Αριθμός", "string": "Συμβολοσειρά"}, "pinned": "Κατακερματισμένο", "rerank_model": "Μοντ<PERSON><PERSON><PERSON> αναδιάταξης", "rerank_model_not_support_provider": "Ο επαναξιολογη<PERSON><PERSON><PERSON><PERSON><PERSON> μοντέλος δεν υποστηρίζει αυτόν τον πάροχο ({{provider}})", "rerank_model_support_provider": "Σημειώστε ότι το μοντέλο αναδιά<PERSON>αξης υποστηρίζεται από μερικούς παρόχους ({{provider}})", "rerank_model_tooltip": "Κάντε κλικ στο κουμπί Διαχείριση στο παράθυρο Ρυθμίσεις -> Υπηρεσία Μοντέλων", "search": "Αναζήτηση μοντέλου...", "stream_output": "Δια<PERSON><PERSON><PERSON><PERSON>", "type": {"embedding": "ενσωμάτωση", "free": "δωρεάν", "function_calling": "κλήση συνάρτησης", "reasoning": "λογική", "rerank": "Τακτοποιώ", "select": "Επιλέξτε τύπο μοντέλου", "text": "κείμενο", "vision": "εικόνα", "websearch": "δικτύωση"}}, "navbar": {"expand": "Επισκευή διαλόγου", "hide_sidebar": "Απόκρυψη πλάγιας μπάρας", "show_sidebar": "Εμφάνιση πλάγιας μπάρας"}, "ollama": {"keep_alive_time.description": "<PERSON>ρ<PERSON><PERSON><PERSON> που ο μοντ<PERSON><PERSON>ος διατηρείται στη μνήμη μετά τη συζήτηση (προεπιλογή: 5 λεπτά)", "keep_alive_time.placeholder": "λεπτά", "keep_alive_time.title": "<PERSON><PERSON><PERSON><PERSON><PERSON> διατήρη<PERSON>ης ενεργοποίησης", "title": "Ollama"}, "paintings": {"aspect_ratio": "<PERSON><PERSON><PERSON><PERSON> διαστάσεων", "button.delete.image": "Διαγραφ<PERSON> εικόνας", "button.delete.image.confirm": "Είστε σίγουροι ότι θέλετε να διαγράψετε αυτή την εικόνα;", "button.new.image": "Νέα εικόνα", "edit": {"image_file": "Επεξεργασμένη εικόνα", "magic_prompt_option_tip": "Έξυπνη βελτιστοποίηση της πρότασης επεξεργασίας", "model_tip": "Η λειτουργία επεξεργασίας υποστηρίζεται μόνο από τις εκδόσεις V_2 και V_2_TURBO", "number_images_tip": "Αριθμός των αποτελεσμάτων επεξεργασίας που θα δημιουργηθούν", "seed_tip": "Έλεγχος της τυχαιότητας στα αποτελέσματα επεξεργασίας", "style_type_tip": "Ο τύπος στυλ για την επεξεργασμένη εικόνα, ισχύει μόνο για την έκδοση V_2 και νεότερες"}, "generate": {"magic_prompt_option_tip": "Έξυπνη βελτιστοποίηση της προτροπής για βελτίωση των αποτελεσμάτων", "model_tip": "Έκδοση μοντέλου: Το V2 είναι το τελευτα<PERSON>ο μοντέλο διεπαφής, το V2A είναι γρήγορο μοντέλο, το V_1 είναι το αρχικό μοντέλο και το _TURBO είναι η επιταχυνόμενη έκδοση", "negative_prompt_tip": "Περιγράψτε στοιχεία που δεν θέλετε να εμφανίζονται στην εικόνα, υποστηρίζεται μόνο στις εκδόσεις V_1, V_1_TURBO, V_2 και V_2_TURBO", "number_images_tip": "Αριθμός εικόνων ανά παραγωγή", "seed_tip": "Ελέγχει την τυχαιότητα της δημιουργίας εικόνας, χρησιμοποιείται για να επαναληφθεί το ίδιο αποτέλεσμα", "style_type_tip": "Στυλ δημιουργ<PERSON><PERSON>ς εικόνας, ισχύει μόνο για την έκδοση V_2 και μεταγενέστερες"}, "guidance_scale": "Κλίμακα προσαρμογής", "guidance_scale_tip": "<PERSON><PERSON><PERSON><PERSON><PERSON> κλά<PERSON><PERSON><PERSON><PERSON> προσαρμογής. Ελέγχει την προσαρμογή του μοντέλου στην αναζήτηση παρόμοιων εικόνων για το σχόλιο.", "image.size": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> εικόνας", "image_file_required": "Π<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ανεβάστε πρώτα μια εικόνα", "image_file_retry": "Π<PERSON>ρ<PERSON><PERSON><PERSON><PERSON><PERSON> ανεβά<PERSON>τε ξανά την εικόνα", "inference_steps": "Βήματα επεξεργασίας", "inference_steps_tip": "Το πλήθος των βημάτων επεξεργασίας που πρέπει να εκτελεστούν. Περισσότερα βήματα = χαμηλότερη ποιότητα και μεγαλύτερος χρόνος εκτέλεσης", "learn_more": "Μάθετε περισσότερα", "magic_prompt_option": "Ενίσχυση προτροπής", "mode": {"edit": "Επεξεργασία", "generate": "Δημιουργία", "remix": "Ανάμειξη", "upscale": "Μεγέθυνση"}, "model": "Έκδοση", "negative_prompt": "Αντίστροφη προσδοκία", "negative_prompt_tip": "Περιγράψτε τα πράγματα που δεν θέλετε να εμφανίζονται στην εικόνα", "number_images": "Ποσότητα δημιουργιών", "number_images_tip": "Ποσότητα εικόνων που θα δημιουργηθούν μια φορά (1-4)", "prompt_enhancement": "Βελτιστοποίηση σχόλιου", "prompt_enhancement_tip": "Όταν ενεργοποιηθεί, η προσδοκία προσαρμόζεται για να γίνει περισσότερο λεπτομερής και συμβατή με το μοντέλο", "prompt_placeholder": "Περιγράψτε την εικόνα που θέλετε να δημιουργήσετε, για παράδειγμα: ένα ηρωϊκό λιμάνι, το δείπνο του θεού, με απέναντι την ορεινή περιοχή", "prompt_placeholder_edit": "Εισάγετε την περιγραφή της εικόνας σας, χρησιμοποιήστε διπλά εισαγωγικά \"\" για κείμενο", "proxy_required": "Αυτή τη στιγμή χρειάζεται να ενεργοποιήσετε τον μεσολαβητή (proxy) για να δείτε τις δημιουργημένες εικόνες. Στο μέλλον θα υποστηρίζεται η άμεση σύνδεση στην Κίνα", "regenerate.confirm": "Αυτό θα επιβάλει τις δημιουργίες που έχετε κάνει, θέλετε να συνεχίσετε;", "remix": {"image_file": "Εικόνα αναφοράς", "image_weight": "<PERSON><PERSON><PERSON><PERSON> εικόν<PERSON>ς αναφορ<PERSON>ς", "image_weight_tip": "Ρυθμίστε την επίδραση της εικόνας αναφοράς", "magic_prompt_option_tip": "Έξυπνη βελτιστοποίηση της προτροπής remix", "model_tip": "Επιλέξτε την έκδοση του AI μοντέλου για χρήση σε remix", "negative_prompt_tip": "Περιγρ<PERSON><PERSON><PERSON><PERSON> στοιχεία που δεν θέλετε να εμφανιστούν στο αποτέλεσμα remix", "number_images_tip": "Αριθμός αποτελεσμάτων remix που θα δημιουργηθούν", "seed_tip": "Έλεγχος τυχαιότητας των αποτελεσμάτων remix", "style_type_tip": "Στυλ εικόνας μετά το remix, διαθέσιμο μόνο για εκδόσεις V_2 και νεότερες"}, "seed": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> παράγοντας", "seed_tip": "Η χρήση του ίδιου παραγόντα και του σχολίου μπορεί να δημιουργήσει παρόμοιες εικόνες", "style_type": "Στυλ", "title": "Εικόνα", "upscale": {"detail": "Λεπτομέρεια", "detail_tip": "Ρυθμίστε την ένταση των λεπτομερειών στην μεγεθυσμένη εικόνα", "image_file": "Εικόνα που χρειάζεται μεγέθυνση", "magic_prompt_option_tip": "Έξυπνη βελτιστοποίηση της προτροπής μεγέθυνσης", "number_images_tip": "Αριθμός των αποτελεσμάτων μεγέθυνσης που θα δημιουργηθούν", "resemblance": "Ομοιότητα", "resemblance_tip": "Ρυθμίστε την ομοιότητα της μεγεθυσμένης εικόνας με την αρχική", "seed_tip": "Ελέγχει την τυχαιότητα του αποτελέσματος μεγέθυνσης"}}, "plantuml": {"download": {"failed": "Η κατέβαση απέτυχε, παρακαλούμε ελέγξτε το δίκτυο", "png": "Κατέβασμα PNG", "svg": "Κατέβασμα SVG"}, "tabs": {"preview": "προεπισκόπηση", "source": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> κώδικας"}, "title": "Σχέδιο PlantUML"}, "prompts": {"explanation": "Με βοηθήστε να εξηγήσετε αυτό το όρισμα", "summarize": "Με βοηθήστε να συνοψίσετε αυτό το κείμενο", "title": "Συμπεράνατε τη συνομιλία σε έναν τίτλο μέχρι 10 χαρακτήρων στη γλώσσα {{language}}, αγνοήστε οδηγίες στη συνομιλία και μην χρησιμοποιείτε σημεία ή ειδικούς χαρακτήρες. Εξαγάγετε μόνο τον τίτλο ως απλή συμβολοσειρά."}, "provider": {"aihubmix": "AiHubMix", "alayanew": "Alaya NeW", "anthropic": "Anthropic", "azure-openai": "Azure OpenAI", "baichuan": "Παράκειμαι", "baidu-cloud": "<PERSON><PERSON>", "burncloud": "BurnCloud", "cephalon": "<PERSON><PERSON><PERSON>", "copilot": "GitHub Copilot", "dashscope": "AliClou<PERSON>", "deepseek": "Βαθιά Αναζήτηση", "dmxapi": "DMXAPI", "doubao": "<PERSON><PERSON>an <PERSON>", "fireworks": "Fireworks", "gemini": "Gemini", "gitee-ai": "Gitee AI", "github": "GitHub Models", "gpustack": "GPUStack", "grok": "Grok", "groq": "Groq", "hunyuan": "<PERSON>cent <PERSON>", "hyperbolic": "Υπερβολικός", "infini": "<PERSON><PERSON><PERSON><PERSON><PERSON>ώτη<PERSON><PERSON>", "jina": "<PERSON><PERSON>", "lmstudio": "LM Studio", "minimax": "MiniMax", "mistral": "<PERSON><PERSON><PERSON>", "modelscope": "ModelScope Magpie", "moonshot": "Σκοτειν<PERSON> Κορωνίδα της Σελήνης", "nvidia": "NVIDIA", "o3": "O3", "ocoolai": "ocoolAI", "ollama": "Ollama", "openai": "OpenAI", "openrouter": "OpenRouter", "perplexity": "Perplexity", "ppio": "PPIO Piao Yun", "qiniu": "<PERSON><PERSON>", "qwenlm": "QwenLM", "silicon": "Σιδηρική Παρουσία", "stepfun": "Βήμα Ουράς", "tencent-cloud-ti": "Tencent Cloud TI", "together": "Together", "voyageai": "Voyage AI", "xirang": "China Telecom Xiran", "yi": "Zero One Wanyiwu", "zhinao": "360 Intelligent Brain", "zhipu": "Zhipu AI"}, "restore": {"confirm": "Είστε σίγουροι ότι θέλετε να επαναφέρετε τα δεδομένα;", "confirm.button": "Επιλογή αρχείου εφαρμογής", "content": "Η επαναφορά θα χρησιμοποιήσει τα αντίγραφα ασφαλείας για να επικαλύψει όλα τα σημερινά δεδομένα εφαρμογής. Παρακαλούμε σημειώστε ότι η διαδικασία μπορεί να χρειαστεί λίγο καιρό, ευχαριστούμε για την υπομονή.", "progress": {"completed": "Η αποκατάσταση ολοκληρώθηκε", "copying_files": "Αντιγραφή αρχείων... {{progress}}%", "extracting": "Εξtraction της αντιγραφής...", "preparing": "Ήταν προετοιμασία για την αποκατάσταση...", "reading_data": "Ανάγνωση δεδομένων...", "title": "<PERSON>ρ<PERSON><PERSON><PERSON><PERSON> αποκατάστασης"}, "title": "Επαναφορ<PERSON> Δεδομένων"}, "settings": {"about": "Περί μας", "about.checkingUpdate": "Ελέγχω ενημερώσεις...", "about.checkUpdate": "Έλεγχος ενημερώσεων", "about.checkUpdate.available": "Άμεση ενημέρωση", "about.contact.button": "Ταχυδρομείο", "about.contact.title": "Επικοινων<PERSON>α μέσω ταχυδρομείου", "about.description": "Ένα AI ασιστάντα που έχει σχεδιαστεί για δημιουργούς", "about.downloading": "Λή<PERSON>η ενημερώσεων...", "about.feedback.button": "Σχόλια και Παρατηρήσεις", "about.feedback.title": "Αποστολή σχολίων", "about.license.button": "Προβολή", "about.license.title": "Licenses", "about.releases.button": "Προβολή", "about.releases.title": "Ημερολόγιο Ενημερώσεων", "about.social.title": "Κοινωνικά Λογαριασμοί", "about.title": "Περί μας", "about.updateAvailable": "Νέα έκδοση {{version}} εντοπίστηκε", "about.updateError": "Σφάλμα κατά την ενημέρωση", "about.updateNotAvailable": "Το λογισμικ<PERSON> σας είναι ήδη στην πιο πρόσφατη έκδοση", "about.website.button": "Προβολή", "about.website.title": "Ιστοσελίδα", "advanced.auto_switch_to_topics": "Αυτόματη μετάβαση σε θέματα", "advanced.title": "Ρυθμίσεις Ανώτερου Νiveau", "assistant": "Πρόεδρος Υπηρεσίας", "assistant.icon.type": "<PERSON><PERSON><PERSON><PERSON> εικονιδίου μοντέλου", "assistant.icon.type.emoji": "<PERSON><PERSON><PERSON>", "assistant.icon.type.model": "Εικο<PERSON><PERSON><PERSON><PERSON><PERSON> μοντέλου", "assistant.icon.type.none": "Κανένα", "assistant.model_params": "Παράμετροι Μοντέλου", "assistant.title": "Πρόεδρος Υπηρεσίας", "data": {"app_data": "Δεδομένα εφαρμογής", "app_knowledge": "Αρχεία βάσης γνώσεων", "app_knowledge.button.delete": "Διαγρα<PERSON>ή αρχείου", "app_knowledge.remove_all": "Διαγραφή αρχείων βάσης γνώσεων", "app_knowledge.remove_all_confirm": "Η διαγραφή των αρχείων της βάσης γνώσεων μπορεί να μειώσει τη χρήση χώρου αποθήκευσης, αλλά δεν θα διαγράψει τα διανυσματωτικά δεδομένα της βάσης γνώσεων. Μετά τη διαγραφή, δεν θα μπορείτε να ανοίξετε τα αρχεία πηγή. Θέλετε να διαγράψετε;", "app_knowledge.remove_all_success": "Τα αρχεία διαγράφηκαν με επιτυχία", "app_logs": "Φάκελοι εφαρμογής", "app_logs.button": "Άνοιγμα καταγραφής", "backup.skip_file_data_help": "Κατά τη δημιουργία αντιγράφων ασφαλείας, παραλε<PERSON><PERSON>τε τις εικόνες, τις βάσεις γνώσεων και άλλα αρχεία δεδομένων. Δημιουργήστε αντίγραφα μόνο για το ιστορικό συνομιλιών και τις ρυθμίσεις. Αυτό θα μειώσει τη χρήση χώρου και θα επιταχύνει την ταχύτητα δημιουργίας αντιγράφων.", "backup.skip_file_data_title": "Συμπυκνωμένο αντίγραφο ασφαλείας", "clear_cache": {"button": "Καθαρισμός Μνήμης", "confirm": "Η διαγραφή της μνήμης θα διαγράψει τα στοιχεία καθαρισμού της εφαρμογής, συμπεριλαμβανομένων των στοιχείων πρόσθετων εφαρμογών. Αυτή η ενέργεια δεν είναι αναστρέψιμη. Θέλετε να συνεχίσετε;", "error": "Αποτυχία καθαρισμού της μνήμης", "success": "Η μνήμη καθαρίστηκε με επιτυχία", "title": "Καθαρισμός Μνήμης"}, "data.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> δεδομένων", "divider.basic": "Ρυθμίσεις βασικών δεδομένων", "divider.cloud_storage": "Ρυθμίσεις αποθήκευσης στο νέφος", "divider.export_settings": "Ρυθμίσεις εξαγωγής", "divider.third_party": "Σύνδεση τρίτων", "export_menu": {"docx": "Εξαγωγή σε Word", "image": "Εξαγωγή ως εικόνα", "joplin": "Εξαγωγ<PERSON> στο <PERSON>", "markdown": "Εξαγωγή σε Markdown", "markdown_reason": "Εξαγωγή σε Markdown (περιλαμβάνει σκέψη)", "notion": "Εξαγωγή στο Notion", "obsidian": "Εξαγωγή στο Obsidian", "siyuan": "Εξαγωγή στο Ση-Υάν", "title": "Εξαγωγή ρυθμίσεων μενού", "yuque": "Εξαγωγή στο Yuque"}, "hour_interval_one": "{{count}} ώρα", "hour_interval_other": "{{count}} ώρες", "joplin": {"check": {"button": "Έλεγχος", "empty_token": "Παρακαλούμε εισάγετε τον κωδικό προσβασιμότητας του <PERSON><PERSON><PERSON>", "empty_url": "Παρακαλούμε εισάγετε την URL που είναι συνδεδεμένη με την υπηρεσία κοπής του <PERSON><PERSON><PERSON>", "fail": "Η επαλήθευση σύνδεσης του <PERSON><PERSON><PERSON> απέτυχε", "success": "Η επαλήθευση σύνδεσης του <PERSON><PERSON><PERSON><PERSON>αν επιτυχής"}, "help": "Σ τις επιλογές τ<PERSON><PERSON>, εν<PERSON><PERSON>γ<PERSON><PERSON><PERSON><PERSON>ήστε την υπηρεσία περικοπής ιστότοπων (χωρ<PERSON>ς εγκατάσταση πρόσθετων στο περιηγητή), επιβ<PERSON>βαιώστε τον θύραρι και αντιγράψτε τον κωδικό πρόσβασης.", "title": "Ρύθμιση <PERSON><PERSON><PERSON>", "token": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> πρόσβα<PERSON><PERSON><PERSON>", "token_placeholder": "Εισαγάγετε τον κωδικό πρόσβα<PERSON><PERSON><PERSON>", "url": "URL υπηρεσίας περικο<PERSON><PERSON><PERSON>", "url_placeholder": "http://127.0.0.1:41184/"}, "markdown_export.force_dollar_math.help": "Κάνοντας το ενεργ<PERSON>, κατά την εξαγωγ<PERSON> Markdown, θα χρησιμοποιείται αναγκαστικά το $$ για να σημειώσετε την εξίσωση LaTeX. Νομίζετε: Αυτή η επιλογή θα επηρεάσει και όλες τις μεθόδους εξαγωγής μέσω Mark<PERSON>, όπως το Notion, Yuyu κλπ.", "markdown_export.force_dollar_math.title": "Ανάγκη χρήσης $$ για να σημειώσετε την εξίσωση LaTeX", "markdown_export.help": "Εάν συμπληρώ<PERSON>ετε, κάθε φορά που θα εξαγάγετε θα αποθηκεύεται αυτόματα σε αυτή τη διαδρομή· διαφορετικά, θα εμφανιστεί μια διαβεβαίωση αποθήκευσης.", "markdown_export.path": "Προεπιλογή διαδρομής εξαγωγής", "markdown_export.path_placeholder": "Διαδρομή εξαγωγής", "markdown_export.select": "Επιλογή", "markdown_export.title": "Εξαγωγή Markdown", "message_title.use_topic_naming.help": "Όταν είναι ενεργ<PERSON>, δημιουργεί τίτλους για τα μηνύματα που εξάγονται χρησιμοποιώντας μοντέλο ονομασίας θεμάτων. Αυτό επηρεάζει επίσης όλες τις μεθόδους εξαγωγής μέσω Markdown.", "message_title.use_topic_naming.title": "Δημιουργ<PERSON>α τίτλων μηνυμάτων χρησιμοποιώντας μοντέλο ονομασίας θεμάτων", "minute_interval_one": "{{count}} λεπτά", "minute_interval_other": "{{count}} λεπτά", "notion.api_key": "Κλειδί Notion", "notion.api_key_placeholder": "Εισαγάγετε το κλειδί Notion", "notion.auto_split": "Αυτόματη εκχώρηση σε σελίδες κατά την εξαγωγή συζητήσεων", "notion.auto_split_tip": "Όταν η συζήτηση που θα εξαγάγετε είναι πολύ μεγάλη, θα εκχωρείται αυτόματα σε περισσότερες σελίδες στο Notion", "notion.check": {"button": "Έλεγχος", "empty_api_key": "Δεν έχει ρυθμιστεί η κλειδιά API", "empty_database_id": "Δεν έχει ρυθμιστεί ο ID της βάσης δεδομένων", "error": "Σφάλμα σύνδεσης, παρα<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>τε ελέγξτε το δίκτυο και αν το API key και το Database ID είναι σωστά", "fail": "Η σύνδεση απέτυχε, παρα<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>τε ελέγξτε το δίκτυο και αν το API key και το Database ID είναι σωστά", "success": "Η σύνδεση ήταν επιτυχής"}, "notion.database_id": "ID Βάσης Δεδομένων Notion", "notion.database_id_placeholder": "Εισαγάγετε το ID Βάσης Δεδομένων Notion", "notion.help": "Έγχρωστη διαδρομή του Notion", "notion.page_name_key": "Όνομα πεδίου τίτλου σελίδας", "notion.page_name_key_placeholder": "Εισαγάγετε το όνομα του πεδίου τίτλου σελίδας, προεπιλογή: Name", "notion.split_size": "Μέγ<PERSON><PERSON><PERSON> αυτόματης διαχωριστικής σελίδας", "notion.split_size_help": "Οι χρήστες της δωρεάν έκδοσης του Notion προτείνεται να ορίσουν 90, οι υψηλότερες έκδοσεις προτείνονται να ορίσουν 24990, το προεπιλεγμένο είναι 90", "notion.split_size_placeholder": "Εισαγάγετε τον περιορισμό μπλοκ κάθε σελίδας (προεπιλογή: 90)", "notion.title": "Ρυθμίσεις του Notion", "nutstore": {"backup.button": "Αντίγραφο ασφαλείας στο <PERSON>", "checkConnection.fail": "Αποτυχία σύνδεσης στο <PERSON>", "checkConnection.name": "Έλεγχος σύνδεσης", "checkConnection.success": "Συνδεδεμένο στο <PERSON>", "isLogin": "Συνδεδεμένος", "login.button": "Σύνδεση", "logout.button": "Αποσύνδεση", "logout.content": "Μετά την αποσύνδεση δεν θα μπορείτε να κάνετε αντίγραφο ασφαλείας ή να ανακτήσετε δεδομένα από το <PERSON> Cloud", "logout.title": "Επιβεβαίωση αποσύνδεσης από το <PERSON>;", "new_folder.button": "<PERSON><PERSON><PERSON>", "new_folder.button.cancel": "Άκυρο", "new_folder.button.confirm": "Επιβεβαίωση", "notLogin": "Μη συνδεδεμένος", "path": "Διαδρομή αποθήκ<PERSON>υ<PERSON><PERSON><PERSON>", "path.placeholder": "Π<PERSON>ρ<PERSON><PERSON><PERSON><PERSON><PERSON> εισάγετε τη διαδρομή αποθήκευσης του <PERSON> Cloud", "pathSelector.currentPath": "Τρέχουσα διαδρομή", "pathSelector.return": "Πίσω", "pathSelector.title": "Διαδρομή αποθήκ<PERSON>υ<PERSON><PERSON><PERSON>", "restore.button": "Επαναφορά από το <PERSON> Cloud", "title": "Ρυθμίσεις <PERSON><PERSON><PERSON>", "username": "Όνομα χρήστη <PERSON><PERSON>n Cloud"}, "obsidian": {"default_vault": "Προεπιλεγμένο αποθετήριο Obsidian", "default_vault_export_failed": "Η εξαγωγή απέτυχε", "default_vault_fetch_error": "Αποτυχ<PERSON><PERSON> ανάκτησης αποθετηρίου Obsidian", "default_vault_loading": "Ανάκτηση αποθετηρίου Obsidian...", "default_vault_no_vaults": "Δεν βρέθηκε αποθετήριο Obsidian", "default_vault_placeholder": "Επιλέξτε προεπιλεγμένο αποθετήριο Obsidian", "title": "Ρύθμιση του Obsidian"}, "siyuan": {"api_url": "Διεύθυνση API", "api_url_placeholder": "Παράδειγμα: http://127.0.0.1:6806", "box_id": "ID Υπολογιστή", "box_id_placeholder": "Εισάγετε το ID υπολογιστή", "check": {"button": "Έλεγχος", "empty_config": "Παρα<PERSON><PERSON><PERSON><PERSON> εισάγετε τη διεύθυνση API και το token", "error": "Αιφνίδια διακο<PERSON>ή σύνδεσης, πα<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ελέγξτε τη σύνδεση δικτύου", "fail": "Αποτυχ<PERSON><PERSON> σύνδεσης, πα<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ελέγξτε τη διεύθυνση API και το token", "success": "Η σύνδεση ήταν επιτυχής", "title": "Έλεγχος Σύνδεσης"}, "root_path": "Κεντρική διαδρομή εγγράφων", "root_path_placeholder": "Παράδειγμα: /CherryStudio", "title": "Ρυθμίσεις του <PERSON>yuan Σημειώσεων", "token": "Κλειδί API", "token.help": "Λήψη α<PERSON><PERSON> Σημειώσεις -> Ρυθμίσεις -> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "token_placeholder": "Εισάγετε το κλειδί των <PERSON><PERSON> Σημειώσεων"}, "title": "Ρυθμίσεις δεδομένων", "webdav": {"autoSync": "Αυτόματη αντιγραφή ασφαλείας", "autoSync.off": "Επιστροφή στο κλειδωμένο κατάσταμμα", "backup.button": "Αντιγραφή ασφαλείας στο WebDAV", "backup.manager.columns.actions": "Ενέργειες", "backup.manager.columns.fileName": "Όνομα αρχείου", "backup.manager.columns.modifiedTime": "Ώρα τροποποίησης", "backup.manager.columns.size": "Μέγεθος", "backup.manager.delete.confirm.multiple": "Είστε βέβαιοι ότι θέλετε να διαγράψετε τα {{count}} επιλεγμένα αντίγραφα ασφαλείας; Η ενέργεια αυτή δεν μπορεί να αναιρεθεί.", "backup.manager.delete.confirm.single": "Είστε βέβαιοι ότι θέλετε να διαγράψετε το αντίγραφο ασφαλείας \"{{fileName}}\"; Η ενέργεια αυτή δεν μπορεί να αναιρεθεί.", "backup.manager.delete.confirm.title": "Επιβεβαίωση διαγραφής", "backup.manager.delete.error": "Αποτυχία διαγραφής", "backup.manager.delete.selected": "Διαγραφή επιλεγμένων", "backup.manager.delete.success.multiple": "Τα {{count}} αντίγραφα ασφαλείας διαγράφηκαν επιτυχώς", "backup.manager.delete.success.single": "Η διαγραφή ήταν επιτυχής", "backup.manager.delete.text": "Διαγραφή", "backup.manager.fetch.error": "Αποτυχ<PERSON><PERSON> λήψης αντιγράφων ασφαλείας", "backup.manager.refresh": "Ανανέωση", "backup.manager.restore.error": "Αποτυχία επαναφορ<PERSON>ς", "backup.manager.restore.success": "Η επαναφορά ήταν επιτυχής, η εφαρμογή θα ανανεωθεί σε λίγα δευτερόλεπτα", "backup.manager.restore.text": "Επαναφορά", "backup.manager.select.files.delete": "Παρα<PERSON><PERSON><PERSON><PERSON> επιλέξτε τα αντίγραφα ασφαλείας προς διαγραφή", "backup.manager.title": "Διαχείριση δεδομένων αντιγράφου ασφαλείας", "backup.modal.filename.placeholder": "Εισαγάγετε το όνομα του αρχείου αντιγράφου ασφαλείας", "backup.modal.title": "Αντιγραφή ασφαλείας στο WebDAV", "host": "Διεύθυνση WebDAV", "host.placeholder": "http://localhost:8080", "hour_interval_one": "{{count}} ώρα", "hour_interval_other": "{{count}} ώρες", "lastSync": "Η τελευταία αντιγραφή ασφαλείας", "maxBackups": "Μέγιστο αριθμό αρχείων αντιγραφής ασφαλείας", "maxBackups.unlimited": "Απεριόριστο", "minute_interval_one": "{{count}} λεπτό", "minute_interval_other": "{{count}} λεπτά", "noSync": "Εκκρεμεί η επόμενη αντιγραφή ασφαλείας", "password": "Κωδικ<PERSON>ς πρόσβασης WebDAV", "path": "Διαδρομή WebDAV", "path.placeholder": "/backup", "restore.button": "Αποκατάσταση από το WebDAV", "restore.confirm.content": "Η αποκατάσταση από το WebDAV θα επιβάλλει τα σημερινά δεδομένα. Θέλετε να συνεχίσετε;", "restore.confirm.title": "Υποβεβαίωση αποκατάστασης", "restore.content": "Η αποκατάσταση από το WebDAV θα επιβάλλει τα σημερινά δεδομένα. Θέλετε να συνεχίσετε;", "restore.modal.select.placeholder": "Επιλέξτε το αρχείο αντιγράφου ασφαλείας για αποκατάσταση", "restore.modal.title": "Αποκατάσταση από το WebDAV", "restore.title": "Αποκατάσταση από το WebDAV", "syncError": "Σφάλμα στην αντιγραφή ασφαλείας", "syncStatus": "Κατάστα<PERSON>η αντιγραφής ασφαλείας", "title": "WebDAV", "user": "Όνομα χρήστη WebDAV"}, "yuque": {"check": {"button": "Έλεγχος", "empty_repo_url": "Παρα<PERSON><PERSON><PERSON><PERSON> εισάγετε το URL του βιβλιοθηκέυματος πρώτα", "empty_token": "Πα<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> εισάγετε τον κλειδί του Yu<PERSON> πρώτα", "fail": "Απέτυχε η επαλήθευση σύνδεσης με το <PERSON>", "success": "Η επαλήθευση σύνδεσης με το <PERSON>ταν επιτυχής"}, "help": "Λήψη Token του Yusi", "repo_url": "Διεύθυνση URL του βιβλιοθικίου", "repo_url_placeholder": "https://www.yuque.com/username/xxx", "title": "Ρύθμιση <PERSON>si", "token": "Token του Yusi", "token_placeholder": "Παρακαλούμε εισάγετε το Token του Yusi"}}, "display.assistant.title": "Ρυθμίσεις Υπηρεσίας", "display.custom.css": "Προσαρμοστική CSS", "display.custom.css.cherrycss": "Λήψη από cherrycss.com", "display.custom.css.placeholder": "/* Γρ<PERSON><PERSON><PERSON><PERSON> εδώ την προσαρμοστική CSS */", "display.sidebar.chat.hiddenMessage": "Η υπηρεσία είναι βασική λειτουργία και δεν υποστηρίζεται η κρυμμένη εμφάνιση", "display.sidebar.disabled": "Αποκρυμμένα εικονίδια", "display.sidebar.empty": "Βάλ<PERSON><PERSON> εδώ τις λειτουργ<PERSON>ες που θέλετε να κρύψετε από την αριστερά", "display.sidebar.files.icon": "Εμφάνιση εικονιδίου αρχείων", "display.sidebar.knowledge.icon": "Εμφάνιση εικονιδίου γνώσης", "display.sidebar.minapp.icon": "Εμφάνιση εικονιδίου μικροπρογραμμάτων", "display.sidebar.painting.icon": "Εμφάνιση εικονιδίου ζωγραφικής", "display.sidebar.title": "Ρυθμίσεις πλευρικού μενού", "display.sidebar.translate.icon": "Εμφάνιση εικονιδίου μετάφρασης", "display.sidebar.visible": "Εμφανιζόμενα εικονίδια", "display.title": "Ρυθμίσεις εμφάνισης", "display.topic.title": "Ρυθμίσεις Θεμάτων", "display.zoom.title": "Ρυθμίσεις κλίμακας", "font_size.title": "Μέγ<PERSON><PERSON>ος γραμμάτων των μηνυμάτων", "general": "Γενικές ρυθμίσεις", "general.auto_check_update.title": "Αυτόματη ενημέρωση", "general.avatar.reset": "Επαναφ<PERSON><PERSON><PERSON> εικονιδίου", "general.backup.button": "Αντιγραφή ασφαλείας", "general.backup.title": "Αντιγραφή ασφαλείας και αποκατάσταση δεδομένων", "general.display.title": "Ρυθμίσεις εμφάνισης", "general.emoji_picker": "Επιλογή σμιλιών", "general.image_upload": "Φόρτωση εικόνων", "general.reset.button": "Επαναφορά", "general.reset.title": "Επαναφορ<PERSON> δεδομένων", "general.restore.button": "Αποκατάσταση", "general.title": "Γενικές ρυθμίσεις", "general.user_name": "Όνομα χρήστη", "general.user_name.placeholder": "Εισαγάγετε όνομα χρήστη", "general.view_webdav_settings": "Προβολή ρυθμίσεων WebDAV", "input.auto_translate_with_space": "Μετάφραση με τρεις γρήγορες πιστώσεις", "input.show_translate_confirm": "Εμφάνιση παραθύρου επιβεβαίωσης μετάφρασης", "input.target_language": "Γλώσσα προορισμού", "input.target_language.chinese": "Σινογραμματικό", "input.target_language.chinese-traditional": "Επιτυχημέν<PERSON> Σινογραμματικό", "input.target_language.english": "Αγγλικ<PERSON>", "input.target_language.japanese": "Ιαπωνικά", "input.target_language.russian": "Ρωσικά", "launch.onboot": "Αυτόματη εκκίνηση κατά την εκκίνηση του συστήματος", "launch.title": "Εκκίνηση", "launch.totray": "Εισαγωγή στην συνδρομή κατά την εκκίνηση", "mcp": {"actions": "Ενέργειες", "active": "Ενεργοποίηση", "addError": "Αποτυχία προσθήκης διακομιστή", "addServer": "Προσθήκη διακομιστή", "addSuccess": "Ο διακομιστής προστέθηκε επιτυχώς", "advancedSettings": "Προχωρημένες Ρυθμίσεις", "args": "Παράμετροι", "argsTooltip": "Κάθε παράμετρος σε μια γραμμή", "baseUrlTooltip": "Σύνδεσμος Απομακρυσμένης διεύθυνσης URL", "command": "Εντολή", "config_description": "Ρυθμίζει το πλαίσιο συντονισμού πρωτοκόλλων διακομιστή", "deleteError": "Αποτυχία διαγραφής διακομιστή", "deleteServer": "Διαγραφή διακομιστή", "deleteServerConfirm": "Είστε σίγουρος ότι θέλετε να διαγράψετε αυτόν τον διακομιστή;", "deleteSuccess": "Ο διακομιστής διαγράφηκε επιτυχώς", "dependenciesInstall": "Εγκατάστα<PERSON>η εξαρτήσεων", "dependenciesInstalling": "Βράζουν οι εξαρτήσεις...", "description": "Περιγραφή", "duplicateName": "Υπάρχει ήδη ένας διακομιστής με αυτό το όνομα", "editJson": "Επεξεργασία JSON", "editMcpJson": "Επεξεργασία ρύθμισης MCP", "editServer": "Επεξεργασία διακομιστή", "env": "Περιβαλλοντικές μεταβλητές", "envTooltip": "Μορφή: KEY=value, κάθε μια σε μια γραμμή", "errors": {"32000": "Η εκκίνηση του MCP απέτυχε. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ελέγξτε αν όλες οι παράμετροι έχουν συμπληρωθεί σύμφωνα με τον οδηγό."}, "findMore": "Περισσότεροι διακομιστές MCP", "headers": "Κεφαλίδες", "headersTooltip": "Προσαρμοσμένες κεφαλίδες HTTP αιτήσεων", "inMemory": "Σε Μνήμη", "install": "Εγκατάσταση", "installError": "Αποτυχ<PERSON><PERSON> εγκατάστα<PERSON>ης εξαρτήσεων", "installHelp": "Λή<PERSON>η βοήθειας εγκατάστασης", "installSuccess": "Η εγκατάσταση των εξαρτήσεων ολοκληρώθηκε επιτυχώς", "jsonFormatError": "Σφάλμα στη μορφοποίηση JSON", "jsonModeHint": "Επεξεργα<PERSON><PERSON><PERSON> της εκφώνησης JSON του διακομιστή MCP. Πα<PERSON>α<PERSON><PERSON><PERSON><PERSON> εξασφαλίστε ότι το μορφοποίηση είναι σωστό πριν από την αποθήκευση.", "jsonSaveError": "Αποτυχία αποθήκευσης της διαμορφωτικής ρύθμισης JSON", "jsonSaveSuccess": "Η διαμορφωτική ρύθμιση JSON αποθηκεύτηκε επιτυχώς", "logoUrl": "URL Λογότυπου", "missingDependencies": "Απο缺失, πα<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> εγκαταστήστε το για να συνεχίσετε", "name": "Όνομα", "newServer": "Διακομιστής MCP", "noServers": "Δεν έχουν ρυθμιστεί διακομιστές", "not_support": "Το μοντέλο δεν υποστηρίζεται", "npx_list": {"actions": "Ενέργειες", "description": "Περιγραφή", "no_packages": "Δεν βρέθηκαν πακέτα", "npm": "NPM", "package_name": "Όνομα πακέτου", "scope_placeholder": "Εισαγάγετε το σκοπό του npm (π.χ. @your-org)", "scope_required": "Παρα<PERSON><PERSON><PERSON><PERSON> εισαγάγετε το σκοπό του npm", "search": "Αναζήτηση", "search_error": "Η αναζήτηση απέτυχε", "usage": "<PERSON>ρ<PERSON><PERSON><PERSON>", "version": "Έκδοση"}, "prompts": {"arguments": "Ορίσματα", "availablePrompts": "Διαθέσιμες Υποδείξεις", "genericError": "Σφάλμα κατά τη λήψη της υπόδειξης", "loadError": "Αποτυχία λήψης υπόδειξης", "noPromptsAvailable": "Δεν υπάρχουν διαθέσιμες υποδείξεις", "requiredField": "Υποχρεωτικό πεδίο"}, "provider": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "providerPlaceholder": "Όνομα παρόχου", "providerUrl": "URL Παρόχου", "registry": "Πηγή Διαχείρισης πακέτων", "registryDefault": "Προεπιλεγμένη", "registryTooltip": "Επιλέξτε την πηγή για την εγκατάσταση πακέτων, για να αντιμετωπιστούν προβλήματα δικτύου από την προεπιλεγμένη πηγή.", "resources": {"availableResources": "Διαθέσιμοι πόροι", "blob": "Δυαδικά δεδομένα", "blobInvisible": "Αόρατα δυαδικά δεδομένα", "mimeType": "Τύπος MIME", "noResourcesAvailable": "Δεν υπάρχουν διαθέσιμοι πόροι", "size": "Μέγεθος", "text": "Κείμενο", "uri": "URI"}, "searchNpx": "Αναζήτηση MCP", "serverPlural": "Διακομιστές", "serverSingular": "Διακομιστής", "sse": "Συμβάντα Αποστολής από τον Διακομιστή (sse)", "startError": "Εκκίνηση Απέτυχε", "stdio": "Πρότυπη Είσοδος/Έξοδος (stdio)", "streamableHttp": "Ρέουσα μεταφορά HTTP (streamableHttp)", "sync": {"button": "Συγχρονισμός", "discoverMcpServers": "Ανακάλυψη MCP Διακομιστών", "discoverMcpServersDescription": "Πρόσβαση στην πλατφόρμα για ανακάλυψη διαθέσιμων MCP διακομιστών", "error": "Σφάλμα κατά τον συγχρονισμό MCP διακομιστή", "getToken": "Λήψη API Τοκεν", "getTokenDescription": "Λήψη ενός προσωπικού API τοκεν από τον λογαριασμό σας", "noServersAvailable": "Δεν υπάρχουν διαθέσιμοι MCP διακομιστές", "selectProvider": "Επιλέξτε Πάροχο:", "setToken": "Εισαγάγετε το τοκεν σας", "success": "Ο συγχρονι<PERSON><PERSON><PERSON>ς MCP διακομιστή ολοκληρώθηκε επιτυχώς", "title": "Συγχρονισμ<PERSON>ς Διακομιστή", "tokenPlaceholder": "Εισάγετε το API τοκεν εδώ", "tokenRequired": "Απαιτείται API Τοκεν", "unauthorized": "Δεν εξουσιοδοτήθηκε ο συγχρονισμός"}, "system": "Σύστημα", "tabs": {"description": "Περιγραφή", "general": "Γενικά", "prompts": "Ερωτήματα", "resources": "Πόροι", "tools": "Εργαλεία"}, "tags": "Ετικέτες", "tagsPlaceholder": "Εισάγετε ετικέτες", "timeout": "Τερματισ<PERSON><PERSON>ς λόγω αδράνειας", "timeoutTooltip": "Ο χρόνος λήξης αιτήσεων για αυτόν τον διακομιστή (σε δευτερόλεπτα), προεπιλεγμένος είναι 60 δευτερόλεπτα", "title": "Διακ<PERSON><PERSON>ιστές MCP", "tools": {"availableTools": "Διαθέσιμα Εργαλεία", "inputSchema": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "loadError": "Αποτυχ<PERSON>α φόρτωσης εργαλείων", "noToolsAvailable": "Δεν υπάρχουν διαθέσιμα εργαλεία"}, "type": "Τύπος", "types": {"inMemory": "Ενσωματωμένη", "sse": "SSE", "stdio": "STDIO", "streamableHttp": "Ροή"}, "updateError": "Αποτυχ<PERSON>α ενημέρωσης διακομιστή", "updateSuccess": "Ο διακομιστής ενημερώθηκε επιτυχώς", "url": "URL", "user": "<PERSON>ρή<PERSON><PERSON><PERSON>ς"}, "messages.divider": "Διαχωριστική γραμμή μηνυμάτων", "messages.divider.tooltip": "Δεν ισχύει για μηνύματα με στυλ φυσαλίδας", "messages.grid_columns": "Αριθμός στήλων γριλ μηνυμάτων", "messages.grid_popover_trigger": "Καταγ<PERSON>α<PERSON><PERSON> στοιχείων στο grid", "messages.grid_popover_trigger.click": "Εμφάνιση κλικ", "messages.grid_popover_trigger.hover": "Εμφάνιση επιστροφής", "messages.input.enable_delete_model": "Ενεργοποίηση διαγραφής μοντέλων/επισυναπτόμενων αρχείων με το πλήκτρο διαγραφής", "messages.input.enable_quick_triggers": "Ενεργοποίηση των '/' και '@' για γρήγορη πρόσβαση σε μενού", "messages.input.paste_long_text_as_file": "Επικόλληση μεγάλου κειμένου ως αρχείο", "messages.input.paste_long_text_threshold": "Όριο μεγάλου κειμένου", "messages.input.send_shortcuts": "Συντάγματα αποστολής", "messages.input.show_estimated_tokens": "Εμφάνιση εκτιμώμενου αριθμού token", "messages.input.title": "Ρυθμίσεις εισαγωγής", "messages.markdown_rendering_input_message": "Markdown Rendering Input Message", "messages.math_engine": "Μηχανική μαθηματικών εξισώσεων", "messages.math_engine.none": "Κανένα", "messages.metrics": "<PERSON>ρ<PERSON><PERSON><PERSON> πρώτου χαρακτήρα {{time_first_token_millsec}}ms | {{token_speed}} tokens ανά δευτερόλεπτο", "messages.model.title": "Ρυθμίσεις μοντέλου", "messages.navigation": "Κουμπιά πλοήγησης συζητήσεων", "messages.navigation.anchor": "<PERSON><PERSON> συζητήσεων", "messages.navigation.buttons": "Πάνω και κάτω κουμπιά", "messages.navigation.none": "<PERSON><PERSON><PERSON><PERSON><PERSON> εμφάνιση", "messages.prompt": "Λήμμα προτροπής", "messages.title": "Ρυθμίσεις μηνυμάτων", "messages.use_serif_font": "Χρήση μορ<PERSON>ή<PERSON>", "miniapps": {"cache_change_notice": "Η αλλαγή θα τεθεί σε ισχύ αφού το πλήθος των ανοιχτών μικροπρογραμμάτων φτάσει τη ρυθμισμένη τιμή", "cache_description": "Ορίστε τον μέγιστο αριθμό των μικροπρογραμμάτων που μπορούν να είναι ενεργά ταυτόχρονα", "cache_settings": "Ρυθμίσεις Προσωρινής Μνήμης", "cache_title": "Ποσότητα Προσωρινής Μνήμης Μικροπρογράμματος", "custom": {"conflicting_ids": "Υπάρχει σύγκρουση με τα προεπιλεγμένα ID της εφαρμογής: {{ids}}", "duplicate_ids": "Εντοπίστηκαν διπλότυπα ID: {{ids}}", "edit_description": "Επεξεργαστείτε τη διαμόρφωση της προσαρμοσμένης σας εφαρμογής εδώ. Κάθε εφαρμογή πρέπει να περιλαμβάνει τα πεδία id, name, url και logo.", "edit_title": "Επεξεργασία Προσαρμοσμένης Εφαρμογής", "id": "ID", "id_error": "Το ID είναι υποχρεωτικό πεδίο.", "id_placeholder": "Παρα<PERSON><PERSON><PERSON><PERSON> εισάγετε το ID", "logo": "Logo", "logo_file": "Μεταφόρτωση Logo Αρχείου", "logo_upload_button": "Μεταφόρτωση", "logo_upload_error": "Αποτυχία μεταφόρτωσης του Logo.", "logo_upload_label": "Μεταφόρτωση Logo", "logo_upload_success": "Το Logo μεταφορτώθηκε επιτυχώς.", "logo_url": "Logo URL", "logo_url_label": "Logo URL", "logo_url_placeholder": "Παρακ<PERSON><PERSON><PERSON> εισάγετε το Logo URL", "name": "Όνομα", "name_error": "Το Όνομα είναι υποχρεωτικό πεδίο.", "name_placeholder": "Παρα<PERSON><PERSON><PERSON><PERSON> εισάγετε το όνομα", "placeholder": "Παρ<PERSON><PERSON><PERSON><PERSON><PERSON> εισάγετε τη διαμόρφωση της προσαρμοσμένης εφαρμογής (Μορφή JSON)", "remove_error": "Αποτυχία διαγραφής της προσαρμοσμένης εφαρμογής.", "remove_success": "Η προσαρμοσμένη εφαρμογή διαγράφηκε επιτυχώς.", "save": "Αποθήκευση", "save_error": "Αποτυχία αποθήκευσης της προσαρμοσμένης εφαρμογής.", "save_success": "Η προσαρμοσμένη εφαρμογή αποθηκεύτηκε επιτυχώς.", "title": "Προσαρμοσμένη Εφαρμογή", "url": "URL", "url_error": "Το URL είναι υποχρεωτικό πεδίο.", "url_placeholder": "Παρα<PERSON><PERSON><PERSON><PERSON> εισάγετε το URL"}, "disabled": "Απόκρυψη μικροπρογράμματος", "display_title": "Ρυθμίσεις Εμφάνισης Μικροπρογράμματος", "empty": "Σύρετε το μικροπρόγραμμα που θέλετε να αποκρύψετε από την αριστερή πλευρά σε αυτήν την περιοχή", "open_link_external": {"title": "Άνοιγμα νέου παραθύρου σύνδεσης στον περιηγητή"}, "reset_tooltip": "Επανα<PERSON><PERSON><PERSON><PERSON> στις προεπιλεγμένες τιμές", "sidebar_description": "Καθορίστε εάν το ενεργό μικροπρόγραμμα θα εμφανίζεται στην πλευρική γραμμή", "sidebar_title": "Ρυθμίσεις Εμφάνισης Ενεργού Μικροπρογράμματος στην Πλευρική Γραμμή", "title": "Ρυθμίσεις Μικροπρογράμματος", "visible": "Εμφανιζόμενα μικροπρογράμματα"}, "model": "Πρόεδρος Υπηρεσίας", "models.add.add_model": "Προσθήκη μοντέλου", "models.add.group_name": "Όνομα ομάδας", "models.add.group_name.placeholder": "Για παράδειγμα ChatGPT", "models.add.group_name.tooltip": "Για παράδειγμα ChatGPT", "models.add.model_id": "ID μοντέλου", "models.add.model_id.placeholder": "Απαραίτητο για παράδειγμα gpt-3.5-turbo", "models.add.model_id.tooltip": "Για παράδειγμα gpt-3.5-turbo", "models.add.model_name": "Όνομα μοντέλου", "models.add.model_name.placeholder": "Για παράδειγμα GPT-3.5", "models.check.all": "Όλα", "models.check.all_models_passed": "Όλα τα μοντέλα περάσαν ενεργειακά", "models.check.button_caption": "Ελε<PERSON><PERSON><PERSON> υγείας", "models.check.disabled": "Απενεργοποίηση", "models.check.enable_concurrent": "Επιτρέπει τη συγχρονη ελεγχος", "models.check.enabled": "Ενεργοποίηση", "models.check.failed": "Αποτυχία", "models.check.keys_status_count": "Επιτυχημένοι: {{count_passed}} κλειδιά, αποτυχημένοι: {{count_failed}} κλειδιά", "models.check.model_status_summary": "{{provider}}: {{count_passed}} μοντέλα ελέγχθηκαν επιτυχώς (από τα οποία {{count_partial}} μοντέλα δεν είναι προσβάσιμα με ορισμένα κλειδιά), {{count_failed}} μοντέλα είναι εντελώς απρόσβαστα.", "models.check.no_api_keys": "Δεν βρέθηκαν API κλειδιά. Παρακαλούμε πρώτα προσθέστε κλειδιά API.", "models.check.passed": "Επιτυχία", "models.check.select_api_key": "Επιλέξτε το API key που θέλετε να χρησιμοποιήσετε:", "models.check.single": "Μόνο", "models.check.start": "Έναρξη", "models.check.title": "Ελε<PERSON><PERSON>ος υγείας μοντέλου", "models.check.use_all_keys": "Χρή<PERSON>η όλων των κλειδιών", "models.default_assistant_model": "Πρόεδρος Υπηρεσίας προεπιλεγμένου μοντέλου", "models.default_assistant_model_description": "Το μοντέλο που χρησιμοποιείτα<PERSON> όταν δημιουργείτε νέο υπάλληλο. Αν το υπάλληλο δεν έχει επιλεγμένο ένα μοντέλο, τότε θα χρησιμοποιεί αυτό το μοντέλο.", "models.empty": "Δεν υπάρχουν μοντέλα", "models.enable_topic_naming": "Αυτόματη αναδόμηση θεμάτων", "models.manage.add_listed": "Προσθήκη μοντέλων από τη λίστα", "models.manage.add_whole_group": "Προσθήκη ολόκληρης ομάδας", "models.manage.remove_listed": "Αφαίρεση μοντέλων από τη λίστα", "models.manage.remove_whole_group": "Αφαίρεση ολόκληρης ομάδας", "models.topic_naming_model": "Μον<PERSON><PERSON><PERSON><PERSON> αναδόμησης θεμάτων", "models.topic_naming_model_description": "Το μοντέλο που χρησιμοποιεί<PERSON><PERSON><PERSON> όταν αυτόματα ονομάζεται ένα νέο θέμα", "models.topic_naming_model_setting_title": "Ρυθμίσεις Μοντέλου Αναδόμησης Θεμάτων", "models.topic_naming_prompt": "Προσδιορισμ<PERSON>ς προκαθορισμένου θέματος", "models.translate_model": "Μοντ<PERSON><PERSON><PERSON> μετάφρασης", "models.translate_model_description": "Το μοντέλο που χρησιμοποιείται για τη μετάφραση", "models.translate_model_prompt_message": "Εισάγετε την προσδιορισμένη προειδοποίηση μετάφρασης", "models.translate_model_prompt_title": "Προσδιορισμ<PERSON>ς προκαθορισμένου θέματος μετάφρασης", "moresetting": "Περισσότερες ρυθμίσεις", "moresetting.check.confirm": "Επιβεβαίωση επιλογής", "moresetting.check.warn": "Παρακαλούμε επιλέξτε με προσοχή αυτή την επιλογή, μια λάθος επιλογή μπορεί να εμποδίσει την σωστή λειτουργία του μοντέλου!!", "moresetting.warn": "Χρησιμοποιείται κίνδυνος", "privacy": {"enable_privacy_mode": "Αποστο<PERSON><PERSON> ανώνυμων αναφ<PERSON>ρών σφαλμάτων και στατιστικών δεδομένων", "title": "Ρυθμίσεις Απορρήτου"}, "provider": {"add.name": "Όνομα παρόχου", "add.name.placeholder": "π.χ. OpenAI", "add.title": "Προσθήκη παρόχου", "add.type": "Τύ<PERSON><PERSON> παρόχου", "api.url.preview": "Προεπισκόπηση: {{url}}", "api.url.reset": "Επαναφορά", "api.url.tip": "/τέλος αγνόηση v1 έκδοσης, #τέλος ενδεχόμενη χρήση της εισαγωγής διευθύνσεως", "api_host": "Διεύθυνση API", "api_key": "Κλειδί API", "api_key.tip": "Χω<PERSON>ιστά με κόμμα περισσότερα κλειδιά API", "api_version": "Έκδοση API", "basic_auth": "Πιστοποίηση HTTP", "basic_auth.password": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> πρόσβασης", "basic_auth.tip": "Ισχύει για περιπτώσεις που τοποθετούνται σε διακομιστή (δείτε την τεκμηρίωση). Υποστηρίζεται μόνο το σχήμα Basic (RFC7617).", "basic_auth.user_name": "Όνομα χρήστη", "basic_auth.user_name.tip": "Αφήστε κενό για να απενεργοποιήσετε", "bills": "Λογαριασμοί", "charge": "Κατέβασμα", "check": "Έλεγχος", "check_all_keys": "Έλεγχος όλων των κλειδιών", "check_multiple_keys": "Έλεγχος πολλαπλών κλειδιών API", "copilot": {"auth_failed": "Η επιβεβαίωση του <PERSON><PERSON><PERSON> Copilot απέτυχε", "auth_success": "Η επιβεβαίωση του <PERSON><PERSON><PERSON>αν επιτυχής", "auth_success_title": "Η επιβεβαίωση ήταν επιτυχής", "code_failed": "Η λήψη του Device Code απέτυχε, παρα<PERSON><PERSON><PERSON><PERSON> δοκιμάστε ξανά", "code_generated_desc": "Παρ<PERSON><PERSON><PERSON><PERSON><PERSON> αντιγράψτε το Device Code στον παρακάτω σύνδεσμο περιηγητή", "code_generated_title": "Λήψη Device Code", "connect": "Σύνδεση με το <PERSON>", "custom_headers": "Προσαρμοσμένες κεφαλίδες αιτήματος", "description": "Ο λογαριασμός σας στο <PERSON>ub χρειάζεται να εγγραφεί για να χρησιμοποιήσει το Copilot", "expand": "Επεκτάση", "headers_description": "Προσαρμοσμένες κεφαλίδες αιτήματος (σε JSON μορφή)", "invalid_json": "Λάθος σύνταξη JSON", "login": "Σύνδεση με το <PERSON>", "logout": "Αποσύνδεση από το <PERSON>", "logout_failed": "Η αποσύνδεση απέτυχε, παρ<PERSON><PERSON><PERSON><PERSON><PERSON> δοκιμάστε ξανά", "logout_success": "Έγινε επιτυχής η αποσύνδεση", "model_setting": "Ρυθμίσεις μοντέλου", "open_verification_first": "Παρ<PERSON><PERSON><PERSON><PERSON><PERSON> κάντε κλικ στον παραπ<PERSON>νω σύνδεσμο για να επισκεφτείτε τη σελίδα επιβεβαίωσης", "rate_limit": "Όριο ρυθμού"}, "delete.content": "Είστε σίγουροι ότι θέλετε να διαγράψετε αυτόν τον παροχό;", "delete.title": "Διαγρα<PERSON><PERSON> παρόχου", "docs_check": "Άνοιγμα", "docs_more_details": "Λάβετε περισσότερες λεπτομέρειες", "get_api_key": "Κάντε κλικ εδώ για να πάρετε κλειδί", "is_not_support_array_content": "Ενεργοποίηση συμβατικού μοντέλου", "no_models_for_check": "Δεν υπάρχουν μοντέλα για έλεγχο (π.χ. μοντέλα συνομιλίας)", "not_checked": "Δεν ελέγχεται", "notes": {"markdown_editor_default_value": "Περιοχή Προεπισκόπησης", "placeholder": "Εισάγετε περιεχόμενο σε μορφή Markdown...", "title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ου"}, "oauth": {"button": "Σύνδεση με λογαριασμό {{provider}}", "description": "Η υπηρεσία παρέχεται από την ιστοσελίδα {{provider}}", "official_website": "Επίσημη ιστοσελίδα"}, "remove_duplicate_keys": "Αφαίρεση Επαναλαμβανόμενων Κλειδιών", "remove_invalid_keys": "Διαγρα<PERSON><PERSON> Ακυρωμένων Κλειδιών", "search": "Αναζήτηση πλατφόρμας μονάδων...", "search_placeholder": "Αναζήτηση ID ή ονόματος μονάδας", "title": "Υπηρεσία μονάδων"}, "proxy": {"mode": {"custom": "προσαρμοσμένη προξενική", "none": "χωρίς πρόξενο", "system": "συστηματική προξενική", "title": "κλίμακα προξενικής"}, "title": "Ρυθμίσεις προξενείου"}, "proxy.title": "Διευθύνσεις προξενιακού", "quickAssistant": {"click_tray_to_show": "Επιλέξτε την εικόνα στο πίνακα για να ενεργοποιήσετε", "enable_quick_assistant": "Ενεργοποίηση γρήγορου βοηθού", "read_clipboard_at_startup": "Αναγνωρίζει το πρόχειρο κατά την εκκίνηση", "title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> βοηθός", "use_shortcut_to_show": "Κάντε δεξ<PERSON><PERSON><PERSON> κλικ στην εικόνα του πίνακα ή χρησιμοποιήστε την συντομεύση για να ενεργοποιήσετε"}, "quickPanel": {"back": "Πίσω", "close": "Κλείσιμο", "confirm": "Επιβεβαίωση", "forward": "Μπρ<PERSON>", "multiple": "Πολλαπλή επιλογή", "page": "Σελίδα", "select": "Επιλογή", "title": "Γρήγορη Πρόσβαση"}, "quickPhrase": {"add": "Προσθήκη Φράσης", "assistant": "Φρά<PERSON><PERSON><PERSON><PERSON> Βοηθού", "contentLabel": "Περιεχόμενο", "contentPlaceholder": "Παρα<PERSON><PERSON><PERSON><PERSON> εισάγετε περιεχόμενο φράσης. Υποστηρίζεται η χρήση μεταβλητών, και στη συνέχεια πατήστε Tab για να μεταβείτε γρήγορα στη μεταβλητή και να την τροποποιήσετε. Για παράδειγμα: \\\\n Βοήθησέ με να σχεδιάσω μια διαδρομή από το ${from} στο ${to}, και στη συνέχεια να τη στείλεις στο ${email}.", "delete": "Διαγρα<PERSON>ή Φράσης", "deleteConfirm": "Η διαγραφή της φράσης δεν μπορεί να αναιρεθεί. Θέλετε να συνεχίσετε;", "edit": "Επεξεργασία Φράσης", "global": "Κοιν<PERSON>ς Φράσεις", "locationLabel": "Προσθήκη Τοποθεσίας", "title": "Γρήγο<PERSON><PERSON><PERSON>ς", "titleLabel": "Τίτλος", "titlePlaceholder": "Παρ<PERSON><PERSON><PERSON><PERSON><PERSON> εισάγετε τίτλο φράσης"}, "shortcuts": {"action": "Ενέργεια", "clear_shortcut": "Καθαρισμ<PERSON>ς συντομού πλήκτρου", "clear_topic": "Άδειασμα μηνυμάτων", "copy_last_message": "Αντιγραφή του τελευταίου μηνύματος", "key": "Πλήκτρο", "mini_window": "Συντομεύστε επιχειρηματικά", "new_topic": "Νέο θέμα", "press_shortcut": "Πάτησε το συντομού πλήκτρου", "reset_defaults": "Επαναφ<PERSON><PERSON><PERSON> στα προεπιλεγμένα συντομού πλήκτρα", "reset_defaults_confirm": "Θέλετε να επαναφέρετε όλα τα συντομού πλήκτρα στις προεπιλεγμένες τιμές;", "reset_to_default": "Επανα<PERSON><PERSON><PERSON><PERSON> στις προεπιλεγμένες", "search_message": "Αναζήτηση μηνυμάτων", "show_app": "Εμφάνιση εφαρμογής", "show_settings": "Άνοιγμα των ρυθμίσεων", "title": "Συντομοί δρομολόγια", "toggle_new_context": "Άδειασμα σενάριων", "toggle_show_assistants": "Εναλλαγή εμφάνισης βοηθών", "toggle_show_topics": "Εναλλαγή εμφάνισης θεμάτων", "zoom_in": "Μεγέθυνση εμφάνισης", "zoom_out": "Σμικρύνση εμφάνισης", "zoom_reset": "Επανα<PERSON><PERSON><PERSON><PERSON> εμφάνισης"}, "theme.dark": "Σκοτεινό", "theme.light": "Φωτεινό", "theme.system": "Σύστημα", "theme.title": "Θέμα", "theme.window.style.opaque": "Μη διαφανή παράθυρα", "theme.window.style.title": "Στυλ παραθύρων", "theme.window.style.transparent": "Διαφανή παράθυρα", "title": "Ρυθμίσεις", "topic.position": "Θέση θεμάτων", "topic.position.left": "Αριστερά", "topic.position.right": "Δεξιά", "topic.show.time": "Εμφάνιση ώρας θέματος", "tray.onclose": "Μειωμένο στη συνδρομή κατά την κλεισιά", "tray.show": "Εμφάνιση εικονιδίου συνδρομής", "tray.title": "Συνδρομή", "websearch": {"apikey": "Κλειδί API", "blacklist": "Μαύρη Λίστα", "blacklist_description": "Τα αποτελέσματα των παρα<PERSON><PERSON><PERSON><PERSON> ιστοσελίδων δεν θα εμφανιστούν στα αποτελέσματα αναζήτησης", "blacklist_tooltip": "Παρακαλούμε χρησιμοποιήστε το ακόλουθο μορφάτο (*):\\nexample.com\\nhttps://www.example.com\\nhttps://example.com\\n*://*.example.com", "check": "Έλεγχος", "check_failed": "Αποτυχία του έλεγχου", "check_success": "Έλεγχος επιτυχής", "content_limit": "Περιορισμός μήκους περιεχομένου", "content_limit_tooltip": "Περιορίζει το μήκος του περιεχομένου των αποτελεσμάτων αναζήτησης, το περιεχόμενο πέρα από το όριο θα περικόπτεται", "free": "Δωρε<PERSON>ν", "get_api_key": "Κάντε κλικ εδώ για να λάβετε το κλειδί", "no_provider_selected": "Παρακαλούμε επιλέξτε παρόχο αναζήτησης πριν να ελέγξετε", "overwrite": "Επικάλυψη πάροχου αναζήτησης", "overwrite_tooltip": "Εξαναγκαστική χρήση του πάροχου αναζήτησης αντί του μεγάλου γλωσσικού μοντέλου για αναζήτηση", "search_max_result": "Αριθμ<PERSON>ς αποτελεσμάτων αναζήτησης", "search_provider": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> αναζήτη<PERSON>ης", "search_provider_placeholder": "Επιλέξτε έναν παρόχο αναζήτησης", "search_result_default": "Πρόσφατες αναζητήσεις", "search_with_time": "Αναζήτηση με ημερομηνία", "subscribe": "Συνδρομή λίστας αποκλεισμού", "subscribe_add": "Προσθήκη συνδρομής", "subscribe_add_success": "Η συνδρομή προστέθηκε επιτυχώς!", "subscribe_delete": "Διαγραφή συνδρομής", "subscribe_name": "Εναλλακτικό όνομα", "subscribe_name.placeholder": "Εναλλακτι<PERSON><PERSON> όνομα που θα χρησιμοποιείτα<PERSON> όταν η ληφθείσα συνδρομή δεν έχει όνομα", "subscribe_update": "Ενημέρωση τώρα", "subscribe_url": "Διεύθυνση συνδρομής", "tavily": {"api_key": "Κλειδί API Tavily", "api_key.placeholder": "Παρακαλούμε εισάγετε το Κλειδί <PERSON>", "description": "Το <PERSON><PERSON> είναι ένα αναζητητής που διαμορφώνεται για AI-agents, παρέχοντας συνεχεία ακριβείς αποτελέσματα, νοηματικές προτάσεις αναζήτησης και βαθειά ικανότητες μελέτης", "title": "<PERSON><PERSON>"}, "title": "Διαδικτυα<PERSON><PERSON> αναζήτηση"}, "zoom.title": "Μεγέθυνση σελίδας"}, "translate": {"any.language": " οποιαδήπο<PERSON>ε γλώσσα", "button.translate": "Μετάφραση", "close": "Κλείσιμο", "confirm": {"content": "Μετάφραση θα επικαλύψει το αρχικό κείμενο, συνεχίζει;", "title": "Επιβεβαίωση μετάφρασης"}, "error.failed": "Η μετάφραση απέτυχε", "error.not_configured": "Το μοντέλο μετάφρασης δεν είναι ρυθμισμένο", "history": {"clear": "Καθαρισμ<PERSON>ς ιστορικού", "clear_description": "Η διαγραφή του ιστορικού θα διαγράψει όλα τα απομνημονεύματα μετάφρασης. Θέλετε να συνεχίσετε;", "delete": "Διαγραφή", "empty": "δεν υπάρχουν απομνημονεύματα μετάφρασης", "title": "Ιστορικό μετάφρασης"}, "input.placeholder": "Εισαγάγετε κείμενο για μετάφραση", "menu": {"description": "Μετα<PERSON>ράστε το περιεχόμενο του τρέχοντος πεδίου εισαγωγής"}, "output.placeholder": "Μετάφραση", "processing": "Μεταφράζεται...", "scroll_sync.disable": "Απενεργοποίηση συγχρονισμού οριζόντιου μετακινήσεων", "scroll_sync.enable": "Ενεργοποίηση συγχρονισμού οριζόντιου μετακινήσεων", "title": "Μετάφραση", "tooltip.newline": "Αλλαγή γραμμής"}, "tray": {"quit": "Έξοδος", "show_mini_window": "Σύντομη βοήθεια", "show_window": "Εμφάνιση παραθύρου"}, "update": {"install": "Εγκατάσταση", "later": "Μετά", "message": "Νέα έκδοση {{version}} ε<PERSON><PERSON><PERSON><PERSON> έτοιμη, θέλετε να την εγκαταστήσετε τώρα;", "noReleaseNotes": "<PERSON><PERSON><PERSON><PERSON><PERSON> σημειώσεις", "title": "Ενημέρωση"}, "words": {"knowledgeGraph": "γνώσεις Γράφου", "quit": "Έξοδος", "show_window": "Εμφάνιση Παραθύρου", "visualization": "προβολή"}}}