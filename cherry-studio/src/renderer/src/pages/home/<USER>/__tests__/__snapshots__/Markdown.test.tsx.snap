// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`Markdown > rendering > should match snapshot 1`] = `
<div
  class="markdown"
>
  <div
    data-testid="markdown-content"
  >
    # Test Markdown

This is **bold** text.
    <span
      data-testid="has-link-component"
    >
      link
    </span>
    <div
      data-testid="has-code-component"
    >
      <div
        data-id="code-block-1"
        data-testid="code-block"
      >
        <code>
          test code
        </code>
        <button
          type="button"
        >
          Save
        </button>
      </div>
    </div>
    <div
      data-testid="has-table-component"
    >
      <div
        data-block-id="test-block-1"
        data-testid="table-component"
      >
        <table>
          test table
        </table>
        <button
          data-testid="copy-table-button"
          type="button"
        >
          Copy Table
        </button>
      </div>
    </div>
    <span
      data-testid="has-img-component"
    >
      img
    </span>
  </div>
</div>
`;
