"use strict";

var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _extends2 = _interopRequireDefault(require("@babel/runtime/helpers/extends"));
var React = _interopRequireWildcard(require("react"));
var _FolderOpenTwoTone = _interopRequireDefault(require("@ant-design/icons-svg/lib/asn/FolderOpenTwoTone"));
var _AntdIcon = _interopRequireDefault(require("../components/AntdIcon"));
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

var FolderOpenTwoTone = function FolderOpenTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(_AntdIcon.default, (0, _extends2.default)({}, props, {
    ref: ref,
    icon: _FolderOpenTwoTone.default
  }));
};

/**![folder-open](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTE1OSA3NjhoNjEyLjNsMTAzLjQtMjU2SDI2Mi4zeiIgZmlsbD0iI2U2ZjRmZiIgLz48cGF0aCBkPSJNOTI4IDQ0NEg4MjBWMzMwLjRjMC0xNy43LTE0LjMtMzItMzItMzJINDczTDM1NS43IDE4Ni4yYTguMTUgOC4xNSAwIDAwLTUuNS0yLjJIOTZjLTE3LjcgMC0zMiAxNC4zLTMyIDMydjU5MmMwIDE3LjcgMTQuMyAzMiAzMiAzMmg2OThjMTMgMCAyNC44LTcuOSAyOS43LTIwbDEzNC0zMzJjMS41LTMuOCAyLjMtNy45IDIuMy0xMiAwLTE3LjctMTQuMy0zMi0zMi0zMnpNMTM2IDI1NmgxODguNWwxMTkuNiAxMTQuNEg3NDhWNDQ0SDIzOGMtMTMgMC0yNC44IDcuOS0yOS43IDIwTDEzNiA2NDMuMlYyNTZ6bTYzNS4zIDUxMkgxNTlsMTAzLjMtMjU2aDYxMi40TDc3MS4zIDc2OHoiIGZpbGw9IiMxNjc3ZmYiIC8+PC9zdmc+) */
var RefIcon = /*#__PURE__*/React.forwardRef(FolderOpenTwoTone);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'FolderOpenTwoTone';
}
var _default = exports.default = RefIcon;