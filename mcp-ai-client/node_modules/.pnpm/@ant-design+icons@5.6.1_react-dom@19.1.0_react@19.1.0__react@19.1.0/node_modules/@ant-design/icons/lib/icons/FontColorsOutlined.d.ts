import * as React from 'react';
import { AntdIconProps } from '../components/AntdIcon';
/**![font-colors](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTkwNCA4MTZIMTIwYy00LjQgMC04IDMuNi04IDh2ODBjMCA0LjQgMy42IDggOCA4aDc4NGM0LjQgMCA4LTMuNiA4LTh2LTgwYzAtNC40LTMuNi04LTgtOHptLTY1MC4zLTgwaDg1YzQuMiAwIDgtMi43IDkuMy02LjhsNTMuNy0xNjZoMjE5LjJsNTMuMiAxNjZjMS4zIDQgNSA2LjggOS4zIDYuOGg4OS4xYzEuMSAwIDIuMi0uMiAzLjItLjVhOS43IDkuNyAwIDAwNi0xMi40TDU3My42IDExOC42YTkuOSA5LjkgMCAwMC05LjItNi42SDQ2Mi4xYy00LjIgMC03LjkgMi42LTkuMiA2LjZMMjQ0LjUgNzIzLjFjLS40IDEtLjUgMi4xLS41IDMuMi0uMSA1LjMgNC4zIDkuNyA5LjcgOS43em0yNTUuOS01MTYuMWg0LjFsODMuOCAyNjMuOEg0MjQuOWw4NC43LTI2My44eiIgLz48L3N2Zz4=) */
declare const RefIcon: React.ForwardRefExoticComponent<Omit<AntdIconProps, 'ref'> & React.RefAttributes<HTMLSpanElement>>;
export default RefIcon;
