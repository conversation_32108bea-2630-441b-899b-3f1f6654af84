"use strict";

var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _extends2 = _interopRequireDefault(require("@babel/runtime/helpers/extends"));
var React = _interopRequireWildcard(require("react"));
var _FrownOutlined = _interopRequireDefault(require("@ant-design/icons-svg/lib/asn/FrownOutlined"));
var _AntdIcon = _interopRequireDefault(require("../components/AntdIcon"));
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

var FrownOutlined = function FrownOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(_AntdIcon.default, (0, _extends2.default)({}, props, {
    ref: ref,
    icon: _FrownOutlined.default
  }));
};

/**![frown](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTI4OCA0MjFhNDggNDggMCAxMDk2IDAgNDggNDggMCAxMC05NiAwem0zNTIgMGE0OCA0OCAwIDEwOTYgMCA0OCA0OCAwIDEwLTk2IDB6TTUxMiA2NEMyNjQuNiA2NCA2NCAyNjQuNiA2NCA1MTJzMjAwLjYgNDQ4IDQ0OCA0NDggNDQ4LTIwMC42IDQ0OC00NDhTNzU5LjQgNjQgNTEyIDY0em0yNjMgNzExYy0zNC4yIDM0LjItNzQgNjEtMTE4LjMgNzkuOEM2MTEgODc0LjIgNTYyLjMgODg0IDUxMiA4ODRjLTUwLjMgMC05OS05LjgtMTQ0LjgtMjkuMkEzNzAuNCAzNzAuNCAwIDAxMjQ4LjkgNzc1Yy0zNC4yLTM0LjItNjEtNzQtNzkuOC0xMTguM0MxNDkuOCA2MTEgMTQwIDU2Mi4zIDE0MCA1MTJzOS44LTk5IDI5LjItMTQ0LjhBMzcwLjQgMzcwLjQgMCAwMTI0OSAyNDguOWMzNC4yLTM0LjIgNzQtNjEgMTE4LjMtNzkuOEM0MTMgMTQ5LjggNDYxLjcgMTQwIDUxMiAxNDBjNTAuMyAwIDk5IDkuOCAxNDQuOCAyOS4yQTM3MC40IDM3MC40IDAgMDE3NzUuMSAyNDljMzQuMiAzNC4yIDYxIDc0IDc5LjggMTE4LjNDODc0LjIgNDEzIDg4NCA0NjEuNyA4ODQgNTEycy05LjggOTktMjkuMiAxNDQuOEEzNjguODkgMzY4Ljg5IDAgMDE3NzUgNzc1ek01MTIgNTMzYy04NS41IDAtMTU1LjYgNjcuMy0xNjAgMTUxLjZhOCA4IDAgMDA4IDguNGg0OC4xYzQuMiAwIDcuOC0zLjIgOC4xLTcuNEM0MjAgNjM2LjEgNDYxLjUgNTk3IDUxMiA1OTdzOTIuMSAzOS4xIDk1LjggODguNmMuMyA0LjIgMy45IDcuNCA4LjEgNy40SDY2NGE4IDggMCAwMDgtOC40QzY2Ny42IDYwMC4zIDU5Ny41IDUzMyA1MTIgNTMzeiIgLz48L3N2Zz4=) */
var RefIcon = /*#__PURE__*/React.forwardRef(FrownOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'FrownOutlined';
}
var _default = exports.default = RefIcon;