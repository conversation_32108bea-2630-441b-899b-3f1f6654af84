import * as React from 'react';
import { AntdIconProps } from '../components/AntdIcon';
/**![fullscreen-exit](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTM5MSAyNDAuOWMtLjgtNi42LTguOS05LjQtMTMuNi00LjdsLTQzLjcgNDMuN0wyMDAgMTQ2LjNhOC4wMyA4LjAzIDAgMDAtMTEuMyAwbC00Mi40IDQyLjNhOC4wMyA4LjAzIDAgMDAwIDExLjNMMjgwIDMzMy42bC00My45IDQzLjlhOC4wMSA4LjAxIDAgMDA0LjcgMTMuNkw0MDEgNDEwYzUuMS42IDkuNS0zLjcgOC45LTguOUwzOTEgMjQwLjl6bTEwLjEgMzczLjJMMjQwLjggNjMzYy02LjYuOC05LjQgOC45LTQuNyAxMy42bDQzLjkgNDMuOUwxNDYuMyA4MjRhOC4wMyA4LjAzIDAgMDAwIDExLjNsNDIuNCA0Mi4zYzMuMSAzLjEgOC4yIDMuMSAxMS4zIDBMMzMzLjcgNzQ0bDQzLjcgNDMuN0E4LjAxIDguMDEgMCAwMDM5MSA3ODNsMTguOS0xNjAuMWMuNi01LjEtMy43LTkuNC04LjgtOC44em0yMjEuOC0yMDQuMkw3ODMuMiAzOTFjNi42LS44IDkuNC04LjkgNC43LTEzLjZMNzQ0IDMzMy42IDg3Ny43IDIwMGMzLjEtMy4xIDMuMS04LjIgMC0xMS4zbC00Mi40LTQyLjNhOC4wMyA4LjAzIDAgMDAtMTEuMyAwTDY5MC4zIDI3OS45bC00My43LTQzLjdhOC4wMSA4LjAxIDAgMDAtMTMuNiA0LjdMNjE0LjEgNDAxYy0uNiA1LjIgMy43IDkuNSA4LjggOC45ek03NDQgNjkwLjRsNDMuOS00My45YTguMDEgOC4wMSAwIDAwLTQuNy0xMy42TDYyMyA2MTRjLTUuMS0uNi05LjUgMy43LTguOSA4LjlMNjMzIDc4My4xYy44IDYuNiA4LjkgOS40IDEzLjYgNC43bDQzLjctNDMuN0w4MjQgODc3LjdjMy4xIDMuMSA4LjIgMy4xIDExLjMgMGw0Mi40LTQyLjNjMy4xLTMuMSAzLjEtOC4yIDAtMTEuM0w3NDQgNjkwLjR6IiAvPjwvc3ZnPg==) */
declare const RefIcon: React.ForwardRefExoticComponent<Omit<AntdIconProps, 'ref'> & React.RefAttributes<HTMLSpanElement>>;
export default RefIcon;
