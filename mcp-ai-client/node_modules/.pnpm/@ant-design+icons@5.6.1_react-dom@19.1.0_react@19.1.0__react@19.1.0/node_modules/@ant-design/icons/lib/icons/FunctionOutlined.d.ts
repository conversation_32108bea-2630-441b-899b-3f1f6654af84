import * as React from 'react';
import { AntdIconProps } from '../components/AntdIcon';
/**![function](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGRlZnM+PHN0eWxlIC8+PC9kZWZzPjxwYXRoIGQ9Ik04NDEgMzcwYzMtMy4zIDIuNy04LjMtLjYtMTEuM2E4LjI0IDguMjQgMCAwMC01LjMtMi4xaC03Mi42Yy0yLjQgMC00LjYgMS02LjEgMi44TDYzMy41IDUwNC42YTcuOTYgNy45NiAwIDAxLTEzLjQtMS45bC02My41LTE0MS4zYTcuOSA3LjkgMCAwMC03LjMtNC43SDM4MC43bC45LTQuNyA4LTQyLjNjMTAuNS01NS40IDM4LTgxLjQgODUuOC04MS40IDE4LjYgMCAzNS41IDEuNyA0OC44IDQuN2wxNC4xLTY2LjhjLTIyLjYtNC43LTM1LjItNi4xLTU0LjktNi4xLTEwMy4zIDAtMTU2LjQgNDQuMy0xNzUuOSAxNDcuM2wtOS40IDQ5LjRoLTk3LjZjLTMuOCAwLTcuMSAyLjctNy44IDYuNEwxODEuOSA0MTVhOC4wNyA4LjA3IDAgMDA3LjggOS43SDI4NGwtODkgNDI5LjlhOC4wNyA4LjA3IDAgMDA3LjggOS43SDI2OWMzLjggMCA3LjEtMi43IDcuOC02LjRsODkuNy00MzMuMWgxMzUuOGw2OC4yIDEzOS4xYzEuNCAyLjkgMSA2LjQtMS4yIDguOGwtMTgwLjYgMjAzYy0yLjkgMy4zLTIuNiA4LjQuNyAxMS4zIDEuNSAxLjMgMy40IDIgNS4zIDJoNzIuN2MyLjQgMCA0LjYtMSA2LjEtMi44bDEyMy43LTE0Ni43YzIuOC0zLjQgNy45LTMuOCAxMS4zLTEgLjkuOCAxLjYgMS43IDIuMSAyLjhMNjc2LjQgNzg0YzEuMyAyLjggNC4xIDQuNyA3LjMgNC43aDY0LjZhOC4wMiA4LjAyIDAgMDA3LjItMTEuNWwtOTUuMi0xOTguOWMtMS40LTIuOS0uOS02LjQgMS4zLTguOEw4NDEgMzcweiIgLz48L3N2Zz4=) */
declare const RefIcon: React.ForwardRefExoticComponent<Omit<AntdIconProps, 'ref'> & React.RefAttributes<HTMLSpanElement>>;
export default RefIcon;
