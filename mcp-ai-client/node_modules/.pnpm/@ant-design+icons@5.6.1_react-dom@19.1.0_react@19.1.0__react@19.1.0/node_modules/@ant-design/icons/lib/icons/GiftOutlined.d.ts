import * as React from 'react';
import { AntdIconProps } from '../components/AntdIcon';
/**![gift](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTg4MCAzMTBINzMyLjRjMTMuNi0yMS40IDIxLjYtNDYuOCAyMS42LTc0IDAtNzYuMS02MS45LTEzOC0xMzgtMTM4LTQxLjQgMC03OC43IDE4LjQtMTA0IDQ3LjQtMjUuMy0yOS02Mi42LTQ3LjQtMTA0LTQ3LjQtNzYuMSAwLTEzOCA2MS45LTEzOCAxMzggMCAyNy4yIDcuOSA1Mi42IDIxLjYgNzRIMTQ0Yy0xNy43IDAtMzIgMTQuMy0zMiAzMnYyMDBjMCA0LjQgMy42IDggOCA4aDQwdjM0NGMwIDE3LjcgMTQuMyAzMiAzMiAzMmg2NDBjMTcuNyAwIDMyLTE0LjMgMzItMzJWNTUwaDQwYzQuNCAwIDgtMy42IDgtOFYzNDJjMC0xNy43LTE0LjMtMzItMzItMzJ6bS0zMzQtNzRjMC0zOC42IDMxLjQtNzAgNzAtNzBzNzAgMzEuNCA3MCA3MC0zMS40IDcwLTcwIDcwaC03MHYtNzB6bS0xMzgtNzBjMzguNiAwIDcwIDMxLjQgNzAgNzB2NzBoLTcwYy0zOC42IDAtNzAtMzEuNC03MC03MHMzMS40LTcwIDcwLTcwek0xODAgNDgyVjM3OGgyOTh2MTA0SDE4MHptNDggNjhoMjUwdjMwOEgyMjhWNTUwem01NjggMzA4SDU0NlY1NTBoMjUwdjMwOHptNDgtMzc2SDU0NlYzNzhoMjk4djEwNHoiIC8+PC9zdmc+) */
declare const RefIcon: React.ForwardRefExoticComponent<Omit<AntdIconProps, 'ref'> & React.RefAttributes<HTMLSpanElement>>;
export default RefIcon;
