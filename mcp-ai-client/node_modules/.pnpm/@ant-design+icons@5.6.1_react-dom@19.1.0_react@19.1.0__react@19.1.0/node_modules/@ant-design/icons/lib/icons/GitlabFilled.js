"use strict";

var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _extends2 = _interopRequireDefault(require("@babel/runtime/helpers/extends"));
var React = _interopRequireWildcard(require("react"));
var _GitlabFilled = _interopRequireDefault(require("@ant-design/icons-svg/lib/asn/GitlabFilled"));
var _AntdIcon = _interopRequireDefault(require("../components/AntdIcon"));
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

var GitlabFilled = function GitlabFilled(props, ref) {
  return /*#__PURE__*/React.createElement(_AntdIcon.default, (0, _extends2.default)({}, props, {
    ref: ref,
    icon: _GitlabFilled.default
  }));
};

/**![gitlab](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTkxMC41IDU1My4ybC0xMDktMzcwLjhjLTYuOC0yMC40LTIzLjEtMzQuMS00NC45LTM0LjFzLTM5LjUgMTIuMy00Ni4zIDMyLjdsLTcyLjIgMjE1LjRIMzg2LjJMMzE0IDE4MS4xYy02LjgtMjAuNC0yNC41LTMyLjctNDYuMy0zMi43cy0zOS41IDEzLjYtNDQuOSAzNC4xTDExMy45IDU1My4yYy00LjEgMTMuNiAxLjQgMjguNiAxMi4zIDM2LjhsMzg1LjQgMjg5IDM4Ni43LTI4OWMxMC44LTguMSAxNi4zLTIzLjEgMTIuMi0zNi44eiIgLz48L3N2Zz4=) */
var RefIcon = /*#__PURE__*/React.forwardRef(GitlabFilled);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'GitlabFilled';
}
var _default = exports.default = RefIcon;