import * as React from 'react';
import { AntdIconProps } from '../components/AntdIcon';
/**![gitlab](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTkxMy45IDU1Mi4yTDgwNSAxODEuNHYtLjFjLTcuNi0yMi45LTI1LjctMzYuNS00OC4zLTM2LjUtMjMuNCAwLTQyLjUgMTMuNS00OS43IDM1LjJsLTcxLjQgMjEzSDM4OC44bC03MS40LTIxM2MtNy4yLTIxLjctMjYuMy0zNS4yLTQ5LjctMzUuMi0yMy4xIDAtNDIuNSAxNC44LTQ4LjQgMzYuNkwxMTAuNSA1NTIuMmMtNC40IDE0LjcgMS4yIDMxLjQgMTMuNSA0MC43bDM2OC41IDI3Ni40YzIuNiAzLjYgNi4yIDYuMyAxMC40IDcuOGw4LjYgNi40IDguNS02LjRjNC45LTEuNyA5LTQuNyAxMS45LTguOWwzNjguNC0yNzUuNGMxMi40LTkuMiAxOC0yNS45IDEzLjYtNDAuNnpNNzUxLjcgMTkzLjRjMS0xLjggMi45LTEuOSAzLjUtMS45IDEuMSAwIDIuNS4zIDMuNCAzTDgxOCAzOTQuM0g2ODQuNWw2Ny4yLTIwMC45em0tNDg3LjQgMWMuOS0yLjYgMi4zLTIuOSAzLjQtMi45IDIuNyAwIDIuOS4xIDMuNCAxLjdsNjcuMyAyMDEuMkgyMDYuNWw1Ny44LTIwMHpNMTU4LjggNTU4LjdsMjguMi05Ny4zIDIwMi40IDI3MC4yLTIzMC42LTE3Mi45em03My45LTExNi40aDEyMi4xbDkwLjggMjg0LjMtMjEyLjktMjg0LjN6TTUxMi45IDc3Nkw0MDUuNyA0NDIuM0g2MjBMNTEyLjkgNzc2em0xNTcuOS0zMzMuN2gxMTkuNUw1ODAgNzIzLjFsOTAuOC0yODAuOHptLTQwLjcgMjkzLjlsMjA3LjMtMjc2LjcgMjkuNSA5OS4yLTIzNi44IDE3Ny41eiIgLz48L3N2Zz4=) */
declare const RefIcon: React.ForwardRefExoticComponent<Omit<AntdIconProps, 'ref'> & React.RefAttributes<HTMLSpanElement>>;
export default RefIcon;
