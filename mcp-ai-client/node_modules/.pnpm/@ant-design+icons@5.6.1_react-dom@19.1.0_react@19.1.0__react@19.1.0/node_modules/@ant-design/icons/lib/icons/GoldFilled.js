"use strict";

var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _extends2 = _interopRequireDefault(require("@babel/runtime/helpers/extends"));
var React = _interopRequireWildcard(require("react"));
var _GoldFilled = _interopRequireDefault(require("@ant-design/icons-svg/lib/asn/GoldFilled"));
var _AntdIcon = _interopRequireDefault(require("../components/AntdIcon"));
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

var GoldFilled = function GoldFilled(props, ref) {
  return /*#__PURE__*/React.createElement(_AntdIcon.default, (0, _extends2.default)({}, props, {
    ref: ref,
    icon: _GoldFilled.default
  }));
};

/**![gold](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTkwNS45IDgwNi43bC00MC4yLTI0OGMtLjYtMy45LTQtNi43LTcuOS02LjdINTk2LjJjLTMuOSAwLTcuMyAyLjgtNy45IDYuN2wtNDAuMiAyNDhjLS4xLjQtLjEuOS0uMSAxLjMgMCA0LjQgMy42IDggOCA4aDM0MmMuNCAwIC45IDAgMS4zLS4xIDQuMy0uNyA3LjMtNC44IDYuNi05LjJ6bS00NzAuMi0yNDhjLS42LTMuOS00LTYuNy03LjktNi43SDE2Ni4yYy0zLjkgMC03LjMgMi44LTcuOSA2LjdsLTQwLjIgMjQ4Yy0uMS40LS4xLjktLjEgMS4zIDAgNC40IDMuNiA4IDggOGgzNDJjLjQgMCAuOSAwIDEuMy0uMSA0LjQtLjcgNy4zLTQuOCA2LjYtOS4ybC00MC4yLTI0OHpNMzQyIDQ3MmgzNDJjLjQgMCAuOSAwIDEuMy0uMSA0LjQtLjcgNy4zLTQuOCA2LjYtOS4ybC00MC4yLTI0OGMtLjYtMy45LTQtNi43LTcuOS02LjdIMzgyLjJjLTMuOSAwLTcuMyAyLjgtNy45IDYuN2wtNDAuMiAyNDhjLS4xLjQtLjEuOS0uMSAxLjMgMCA0LjQgMy42IDggOCA4eiIgLz48L3N2Zz4=) */
var RefIcon = /*#__PURE__*/React.forwardRef(GoldFilled);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'GoldFilled';
}
var _default = exports.default = RefIcon;