import * as React from 'react';
import { AntdIconProps } from '../components/AntdIcon';
/**![google-circle](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTUxMiA2NEMyNjQuNiA2NCA2NCAyNjQuNiA2NCA1MTJzMjAwLjYgNDQ4IDQ0OCA0NDggNDQ4LTIwMC42IDQ0OC00NDhTNzU5LjQgNjQgNTEyIDY0em0xNjcgNjMzLjZDNjM4LjQgNzM1IDU4MyA3NTcgNTE2LjkgNzU3Yy05NS43IDAtMTc4LjUtNTQuOS0yMTguOC0xMzQuOUMyODEuNSA1ODkgMjcyIDU1MS42IDI3MiA1MTJzOS41LTc3IDI2LjEtMTEwLjFjNDAuMy04MC4xIDEyMy4xLTEzNSAyMTguOC0xMzUgNjYgMCAxMjEuNCAyNC4zIDE2My45IDYzLjhMNjEwLjYgNDAxYy0yNS40LTI0LjMtNTcuNy0zNi42LTkzLjYtMzYuNi02My44IDAtMTE3LjggNDMuMS0xMzcuMSAxMDEtNC45IDE0LjctNy43IDMwLjQtNy43IDQ2LjZzMi44IDMxLjkgNy43IDQ2LjZjMTkuMyA1Ny45IDczLjMgMTAxIDEzNyAxMDEgMzMgMCA2MS04LjcgODIuOS0yMy40IDI2LTE3LjQgNDMuMi00My4zIDQ4LjktNzRINTE2Ljl2LTk0LjhoMjMwLjdjMi45IDE2LjEgNC40IDMyLjggNC40IDUwLjEgMCA3NC43LTI2LjcgMTM3LjQtNzMgMTgwLjF6IiAvPjwvc3ZnPg==) */
declare const RefIcon: React.ForwardRefExoticComponent<Omit<AntdIconProps, 'ref'> & React.RefAttributes<HTMLSpanElement>>;
export default RefIcon;
