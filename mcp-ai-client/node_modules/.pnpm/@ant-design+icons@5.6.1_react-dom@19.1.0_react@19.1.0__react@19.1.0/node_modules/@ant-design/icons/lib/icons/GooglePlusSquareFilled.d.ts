import * as React from 'react';
import { AntdIconProps } from '../components/AntdIcon';
/**![google-plus-square](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTg4MCAxMTJIMTQ0Yy0xNy43IDAtMzIgMTQuMy0zMiAzMnY3MzZjMCAxNy43IDE0LjMgMzIgMzIgMzJoNzM2YzE3LjcgMCAzMi0xNC4zIDMyLTMyVjE0NGMwLTE3LjctMTQuMy0zMi0zMi0zMnpNNTQ4LjUgNjIyLjhjLTQzLjkgNjEuOC0xMzIuMSA3OS44LTIwMC45IDUzLjMtNjktMjYuMy0xMTgtOTkuMi0xMTIuMS0xNzMuNSAxLjUtOTAuOSA4NS4yLTE3MC42IDE3Ni4xLTE2Ny41IDQzLjYtMiA4NC42IDE2LjkgMTE4IDQzLjYtMTQuMyAxNi4yLTI5IDMxLjgtNDQuOCA0Ni4zLTQwLjEtMjcuNy05Ny4yLTM1LjYtMTM3LjMtMy42LTU3LjQgMzkuNy02MCAxMzMuNC00LjggMTc2LjEgNTMuNyA0OC43IDE1NS4yIDI0LjUgMTcwLjEtNTAuMS0zMy42LS41LTY3LjQgMC0xMDEtMS4xLS4xLTIwLjEtLjItNDAuMS0uMS02MC4yIDU2LjItLjIgMTEyLjUtLjMgMTY4LjguMiAzLjMgNDcuMy0zIDk3LjUtMzIgMTM2LjV6TTc5MSA1MzYuNWMtMTYuOC4yLTMzLjYuMy01MC40LjQtLjIgMTYuOC0uMyAzMy42LS4zIDUwLjRINjkwYy0uMi0xNi44LS4yLTMzLjUtLjMtNTAuMy0xNi44LS4yLTMzLjYtLjMtNTAuNC0uNXYtNTAuMWMxNi44LS4yIDMzLjYtLjMgNTAuNC0uMy4xLTE2LjguMy0zMy42LjQtNTAuNGg1MC4ybC4zIDUwLjRjMTYuOC4yIDMzLjYuMiA1MC40LjN2NTAuMXoiIC8+PC9zdmc+) */
declare const RefIcon: React.ForwardRefExoticComponent<Omit<AntdIconProps, 'ref'> & React.RefAttributes<HTMLSpanElement>>;
export default RefIcon;
