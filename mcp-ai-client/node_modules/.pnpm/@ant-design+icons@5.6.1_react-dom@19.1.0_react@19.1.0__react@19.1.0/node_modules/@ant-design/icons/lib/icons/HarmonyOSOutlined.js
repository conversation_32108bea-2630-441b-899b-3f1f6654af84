"use strict";

var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _extends2 = _interopRequireDefault(require("@babel/runtime/helpers/extends"));
var React = _interopRequireWildcard(require("react"));
var _HarmonyOSOutlined = _interopRequireDefault(require("@ant-design/icons-svg/lib/asn/HarmonyOSOutlined"));
var _AntdIcon = _interopRequireDefault(require("../components/AntdIcon"));
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

var HarmonyOSOutlined = function HarmonyOSOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(_AntdIcon.default, (0, _extends2.default)({}, props, {
    ref: ref,
    icon: _HarmonyOSOutlined.default
  }));
};

/**![harmony-o-s](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIGZpbGwtcnVsZT0iZXZlbm9kZCIgdmlld0JveD0iNjQgNjQgODk2IDg5NiIgZm9jdXNhYmxlPSJmYWxzZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cGF0aCBkPSJNNTExLjUgNjVDNzE5Ljk5IDY1IDg4OSAyMzQuMDEgODg5IDQ0Mi41UzcxOS45OSA4MjAgNTExLjUgODIwIDEzNCA2NTAuOTkgMTM0IDQ0Mi41IDMwMy4wMSA2NSA1MTEuNSA2NW0wIDY0QzMzOC4zNiAxMjkgMTk4IDI2OS4zNiAxOTggNDQyLjVTMzM4LjM2IDc1NiA1MTEuNSA3NTYgODI1IDYxNS42NCA4MjUgNDQyLjUgNjg0LjY0IDEyOSA1MTEuNSAxMjlNNzQ1IDg4OXY3MkgyNzh2LTcyeiIgLz48L3N2Zz4=) */
var RefIcon = /*#__PURE__*/React.forwardRef(HarmonyOSOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'HarmonyOSOutlined';
}
var _default = exports.default = RefIcon;