import * as React from 'react';
import { AntdIconProps } from '../components/AntdIcon';
/**![hdd](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTgzMiA2NEgxOTJjLTE3LjcgMC0zMiAxNC4zLTMyIDMydjIyNGg3MDRWOTZjMC0xNy43LTE0LjMtMzItMzItMzJ6TTQ1NiAyMTZjMCA0LjQtMy42IDgtOCA4SDI2NGMtNC40IDAtOC0zLjYtOC04di00OGMwLTQuNCAzLjYtOCA4LThoMTg0YzQuNCAwIDggMy42IDggOHY0OHpNMTYwIDkyOGMwIDE3LjcgMTQuMyAzMiAzMiAzMmg2NDBjMTcuNyAwIDMyLTE0LjMgMzItMzJWNzA0SDE2MHYyMjR6bTU3Ni0xMzZjMjIuMSAwIDQwIDE3LjkgNDAgNDBzLTE3LjkgNDAtNDAgNDAtNDAtMTcuOS00MC00MCAxNy45LTQwIDQwLTQwek0xNjAgNjQwaDcwNFYzODRIMTYwdjI1NnptOTYtMTUyYzAtNC40IDMuNi04IDgtOGgxODRjNC40IDAgOCAzLjYgOCA4djQ4YzAgNC40LTMuNiA4LTggOEgyNjRjLTQuNCAwLTgtMy42LTgtOHYtNDh6IiAvPjwvc3ZnPg==) */
declare const RefIcon: React.ForwardRefExoticComponent<Omit<AntdIconProps, 'ref'> & React.RefAttributes<HTMLSpanElement>>;
export default RefIcon;
