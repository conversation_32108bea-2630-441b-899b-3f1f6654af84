import * as React from 'react';
import { AntdIconProps } from '../components/AntdIcon';
/**![picture](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTkyOCAxNjBIOTZjLTE3LjcgMC0zMiAxNC4zLTMyIDMydjY0MGMwIDE3LjcgMTQuMyAzMiAzMiAzMmg4MzJjMTcuNyAwIDMyLTE0LjMgMzItMzJWMTkyYzAtMTcuNy0xNC4zLTMyLTMyLTMyek0zMzggMzA0YzM1LjMgMCA2NCAyOC43IDY0IDY0cy0yOC43IDY0LTY0IDY0LTY0LTI4LjctNjQtNjQgMjguNy02NCA2NC02NHptNTEzLjkgNDM3LjFhOC4xMSA4LjExIDAgMDEtNS4yIDEuOUgxNzcuMmMtNC40IDAtOC0zLjYtOC04IDAtMS45LjctMy43IDEuOS01LjJsMTcwLjMtMjAyYzIuOC0zLjQgNy45LTMuOCAxMS4zLTEgLjMuMy43LjYgMSAxbDk5LjQgMTE4IDE1OC4xLTE4Ny41YzIuOC0zLjQgNy45LTMuOCAxMS4zLTEgLjMuMy43LjYgMSAxbDIyOS42IDI3MS42YzIuNiAzLjMgMi4yIDguNC0xLjIgMTEuMnoiIC8+PC9zdmc+) */
declare const RefIcon: React.ForwardRefExoticComponent<Omit<AntdIconProps, 'ref'> & React.RefAttributes<HTMLSpanElement>>;
export default RefIcon;
