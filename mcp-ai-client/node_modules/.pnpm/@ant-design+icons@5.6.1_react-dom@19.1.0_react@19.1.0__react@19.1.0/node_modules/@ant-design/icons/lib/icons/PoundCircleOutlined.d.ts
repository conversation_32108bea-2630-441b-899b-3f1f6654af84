import * as React from 'react';
import { AntdIconProps } from '../components/AntdIcon';
/**![pound-circle](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTUxMiA2NEMyNjQuNiA2NCA2NCAyNjQuNiA2NCA1MTJzMjAwLjYgNDQ4IDQ0OCA0NDggNDQ4LTIwMC42IDQ0OC00NDhTNzU5LjQgNjQgNTEyIDY0em0wIDgyMGMtMjA1LjQgMC0zNzItMTY2LjYtMzcyLTM3MnMxNjYuNi0zNzIgMzcyLTM3MiAzNzIgMTY2LjYgMzcyIDM3Mi0xNjYuNiAzNzItMzcyIDM3MnptMTM4LTIwOS44SDQ2OS44di00LjdjMjcuNC0xNy4yIDQzLjktNTAuNCA0My45LTkxLjEgMC0xNC4xLTIuMi0yNy45LTUuMy00MUg2MDdjNC40IDAgOC0zLjYgOC04di0zMGMwLTQuNC0zLjYtOC04LThINDk1Yy03LjItMjIuNi0xMy40LTQ1LjctMTMuNC03MC41IDAtNDMuNSAzNC03MC4yIDg3LjMtNzAuMiAyMS41IDAgNDIuNSA0LjEgNjAuNCAxMC41IDUuMiAxLjkgMTAuNi0yIDEwLjYtNy42di0zOS41YzAtMy4zLTIuMS02LjMtNS4yLTcuNS0xOC44LTcuMi00My44LTEyLjctNzAuMy0xMi43LTkyLjkgMC0xNTEuNSA0NC41LTE1MS41IDEyMC4zIDAgMjYuMyA2LjkgNTIgMTQuNiA3Ny4xSDM3NGMtNC40IDAtOCAzLjYtOCA4djMwYzAgNC40IDMuNiA4IDggOGg2Ny4xYzMuNCAxNC43IDUuOSAyOS40IDUuOSA0NC4yIDAgNDUuMi0yOC44IDgzLjMtNzIuOCA5NC4yLTMuNi45LTYuMSA0LjEtNi4xIDcuOFY3MjJjMCA0LjQgMy42IDggOCA4SDY1MGM0LjQgMCA4LTMuNiA4LTh2LTM5LjhjMC00LjQtMy42LTgtOC04eiIgLz48L3N2Zz4=) */
declare const RefIcon: React.ForwardRefExoticComponent<Omit<AntdIconProps, 'ref'> & React.RefAttributes<HTMLSpanElement>>;
export default RefIcon;
