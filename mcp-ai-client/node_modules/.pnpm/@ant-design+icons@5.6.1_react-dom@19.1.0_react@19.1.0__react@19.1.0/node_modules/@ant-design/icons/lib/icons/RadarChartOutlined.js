"use strict";

var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _extends2 = _interopRequireDefault(require("@babel/runtime/helpers/extends"));
var React = _interopRequireWildcard(require("react"));
var _RadarChartOutlined = _interopRequireDefault(require("@ant-design/icons-svg/lib/asn/RadarChartOutlined"));
var _AntdIcon = _interopRequireDefault(require("../components/AntdIcon"));
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

var RadarChartOutlined = function RadarChartOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(_AntdIcon.default, (0, _extends2.default)({}, props, {
    ref: ref,
    icon: _RadarChartOutlined.default
  }));
};

/**![radar-chart](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTkyNi44IDM5Ny4xbC0zOTYtMjg4YTMxLjgxIDMxLjgxIDAgMDAtMzcuNiAwbC0zOTYgMjg4YTMxLjk5IDMxLjk5IDAgMDAtMTEuNiAzNS44bDE1MS4zIDQ2NmEzMiAzMiAwIDAwMzAuNCAyMi4xaDQ4OS41YzEzLjkgMCAyNi4xLTguOSAzMC40LTIyLjFsMTUxLjMtNDY2YzQuMi0xMy4yLS41LTI3LjYtMTEuNy0zNS44ek04MzguNiA0MTdsLTk4LjUgMzItMjAwLTE0NC43VjE5OS45TDgzOC42IDQxN3pNNDY2IDU2Ny4ybC04OS4xIDEyMi4zLTU1LjItMTY5LjJMNDY2IDU2Ny4yem0tMTE2LjMtOTYuOEw0ODQgMzczLjN2MTQwLjhsLTEzNC4zLTQzLjd6TTUxMiA1OTkuMmw5My45IDEyOC45SDQxOC4xTDUxMiA1OTkuMnptMjguMS0yMjUuOWwxMzQuMiA5Ny4xTDU0MC4xIDUxNFYzNzMuM3pNNTU4IDU2Ny4ybDE0NC4zLTQ2LjktNTUuMiAxNjkuMkw1NTggNTY3LjJ6bS03NC0zNjcuM3YxMDQuNEwyODMuOSA0NDlsLTk4LjUtMzJMNDg0IDE5OS45ek0xNjkuMyA0NzAuOGw4Ni41IDI4LjEgODAuNCAyNDYuNC01My44IDczLjktMTEzLjEtMzQ4LjR6TTMyNy4xIDg1M2w1MC4zLTY5aDI2OS4zbDUwLjMgNjlIMzI3LjF6bTQxNC41LTMzLjhsLTUzLjgtNzMuOSA4MC40LTI0Ni40IDg2LjUtMjguMS0xMTMuMSAzNDguNHoiIC8+PC9zdmc+) */
var RefIcon = /*#__PURE__*/React.forwardRef(RadarChartOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'RadarChartOutlined';
}
var _default = exports.default = RefIcon;